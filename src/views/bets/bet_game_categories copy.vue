<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3"/>

    <page-header pageName="Bet Games" pageSubtitle=" & Categories" />

    <!-- Category Cards Section -->
    <div class="mb-6 bg-white p-4 rounded-lg shadow">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold text-gray-900">Game Categories</h3>
        <button
          @click="openCategoryModal()"
          class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
        >
          <i class="fa fa-plus mr-2"></i>Add Category
        </button>
      </div>
      

      <!-- Horizontal Scrollable Category Cards -->
      <div class="overflow-x-auto">
        <div class="flex space-x-4 pb-2" style="min-width: max-content;">
          <!-- All Categories Card -->
          <div
            @click="selectCategory('')"
            class="flex-shrink-0 w-48 p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 category-card"
            :class="{
              'border-green-500 bg-green-50': selectedCategoryId === '',
              'border-gray-300 bg-white hover:border-gray-400': selectedCategoryId !== ''
            }"
          >
            <div class="flex justify-between items-start mb-2">
              <h4 class="font-medium text-gray-900 truncate">All Games</h4>
              <i class="fa fa-globe text-green-600"></i>
            </div>
            <p class="text-sm text-gray-600">View all games</p>
            <div class="mt-2 text-xs text-gray-500">
              All categories
            </div>
          </div>

          <!-- Category Cards -->
          <div
            v-for="category in categories"
            :key="category.id"
            @click="selectCategory(category.id)"
            class="flex-shrink-0 w-48 p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 category-card"
            :class="{
              'border-blue-500 bg-blue-50': selectedCategoryId === category.id,
              'border-gray-300 bg-white hover:border-gray-400': selectedCategoryId !== category.id
            }"
          >
            <div class="flex justify-between items-start mb-2">
              <h4 class="font-medium text-gray-900 truncate">{{ category.category_name }}</h4>
              <button
                @click.stop="openCategoryModal(category)"
                class="text-gray-400 hover:text-blue-600 transition-colors"
              >
                <i class="fa fa-edit text-sm"></i>
              </button>
            </div>
            <p class="text-sm text-gray-600">{{ category.description || 'No description' }}</p>
            <div class="mt-2 text-xs text-gray-500">
              ID: {{ category.id }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters Section -->
    <div class="mb-6 bg-white p-4 rounded-lg shadow">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Filters</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <!-- Game Name Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Game Name</label>
          <input
            v-model="filters.game_name"
            type="text"
            placeholder="Search by game name"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <!-- Provider Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Provider</label>
          <input
            v-model="filters.provider"
            type="text"
            placeholder="Search by provider"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <!-- Category Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
          <select
            v-model="filters.category_name"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Categories</option>
            <option v-for="category in categories" :key="category.id" :value="category.category_name">
              {{ category.category_name }}
            </option>
          </select>
        </div>

        <!-- Hot Range -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Hot Range</label>
          <div class="flex space-x-2">
            <input
              v-model.number="filters.hot_min"
              type="number"
              min="0"
              placeholder="Min"
              class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <input
              v-model.number="filters.hot_max"
              type="number"
              min="0"
              placeholder="Max"
              class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <!-- Favourite Range -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Favourite Range</label>
          <div class="flex space-x-2">
            <input
              v-model.number="filters.favourite_min"
              type="number"
              min="0"
              placeholder="Min"
              class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <input
              v-model.number="filters.favourite_max"
              type="number"
              min="0"
              placeholder="Max"
              class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <!-- Filter Actions -->
        <div class="flex items-end space-x-2">
          <button
            @click="applyFilters"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Apply Filters
          </button>
          <button
            @click="resetFilters"
            class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
          >
            Reset
          </button>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="mb-6 bg-white p-4 rounded-lg shadow">
      <div class="flex flex-wrap gap-4 items-center">
        <button
          @click="openAddGamesModal"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <i class="fa fa-plus mr-2"></i>Add Games to Category
        </button>

        <button
          @click="saveGamesToCategory"
          :disabled="selectedGameIds.length === 0 || isLoading"
          class="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
        >
          <span v-if="isLoading">Saving...</span>
          <span v-else>Save Selected Games ({{ selectedGameIds.length }})</span>
        </button>

        <!-- Category Filter -->
        <div class="flex items-center space-x-2">
          <label class="text-sm text-gray-600">Filter by Category:</label>
          <select
            v-model="selectedCategoryId"
            @change="loadGames"
            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Games</option>
            <option v-for="category in categories" :key="category.id" :value="category.id">
              {{ category.category_name }}
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- Games Cards -->
    <div class="bg-white p-4 rounded-lg shadow">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold">
          {{ getGamesTitle() }}
          <span v-if="selectedCategoryId" class="text-sm font-normal text-gray-600">
            ({{ categories.find(c => c.id === selectedCategoryId)?.category_name }})
          </span>
        </h3>

        <!-- Pagination Controls -->
        <div class="flex items-center space-x-4">
          <!-- Limit Selector -->
          <div class="flex items-center space-x-2">
            <label class="text-sm text-gray-600">Show:</label>
            <select
              v-model="gamesParams.limit"
              @change="loadGames"
              class="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
          </div>

          <!-- Select All Checkbox -->
          <div class="flex items-center space-x-2">
            <input
              type="checkbox"
              @change="toggleAllGames"
              :checked="selectedGameIds.length === games.length && games.length > 0"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            >
            <label class="text-sm text-gray-600">Select All</label>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="gamesLoading" class="flex justify-center items-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span class="ml-2">Loading games...</span>
      </div>

      <!-- Games Cards Grid -->
      <div v-else-if="games.length > 0">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-4 mb-6">
          <div
            v-for="game in games"
            :key="game.id"
            class="relative border rounded-lg p-4 hover:shadow-md transition-all duration-200"
            :class="{
              'border-blue-500 bg-blue-50': selectedGameIds.includes(game.id),
              'border-gray-300 bg-white': !selectedGameIds.includes(game.id)
            }" >
            <!-- Status Badge -->
            <div class="absolute top-2 right-2 flex flex-col space-y-1">
              <span
                class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                :class="{
                  'bg-green-100 text-green-800': parseInt(game.status) === 1,
                  'bg-red-100 text-red-800': parseInt(game.status) === 0
                }"
              >
                {{ parseInt(game.status) === 1 ? 'Active' : 'Suspended' }}
              </span>

              <!-- Hot Badge -->
              <span
                class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                :class="{
                  'bg-red-100 text-red-800': parseInt(game.hot) > 0,
                  'bg-gray-100 text-gray-600': parseInt(game.hot) === 0 || !game.hot
                }"
              >
                Hot: {{ game.hot || 0 }}
              </span>

              <!-- Favorite Badge -->
              <span
                class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                :class="{
                  'bg-green-100 text-green-800': parseInt(game.favourite) > 0,
                  'bg-gray-100 text-gray-600': parseInt(game.favourite) === 0 || !game.favourite
                }"
              >
                Fav: {{ game.favourite || 0 }}
              </span>
            </div>

            <!-- Checkbox -->
            <div class="absolute top-2 left-2">
              <input
                type="checkbox"
                :value="game.id"
                v-model="selectedGameIds"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              >
            </div>

            <!-- Game Content -->
            <div class="mt-6">
              <h4 class="font-medium text-gray-900 mb-2 pr-16">{{ game.game_name || game.name }}</h4>

              <div class="space-y-1 text-sm text-gray-600 mb-3">
                <p><strong>Provider:</strong> {{ game.provider || 'N/A' }}</p>
                <p><strong>Game ID:</strong> {{ game.id }}</p>
              </div>

              <!-- Current Category Badge -->
              <div class="mb-3">
                <span class="text-xs text-gray-600">Category:</span>
                <span
                  class="category-badge inline-flex px-2 py-1 text-xs font-medium rounded-full ml-1"
                  :class="{
                    'has-category bg-blue-100 text-blue-800': game.category_name,
                    'no-category bg-gray-100 text-gray-600': !game.category_name
                  }"
                >
                  {{ game.category_name || 'No Category' }}
                </span>
              </div>

              <!-- Action Buttons -->
              <div class="space-y-3">
                <!-- First Row: View Button and Status Switch -->
                <div class="flex items-center justify-between">
                  <!-- View Button -->
                  <button
                    @click="openGameModal(game)"
                    class="view-button px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                  >
                    <i class="fa fa-eye mr-1"></i>View
                  </button>

                  <!-- Status Switch -->
                  <div class="flex items-center space-x-2">
                    <span class="text-xs font-medium text-gray-700">Status:</span>
                    <label class="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        :checked="parseInt(game.status) === 1"
                        @change="toggleGameStatus(game)"
                        class="sr-only peer"
                      >
                      <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[1px] after:left-[1px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>

                <!-- Second Row: Hot and Favorite Buttons -->
                <div class="flex items-center justify-between space-x-2">
                  <!-- Hot Button -->
                  <button
                    @click="openHotModal(game)"
                    class="flex-1 px-2 py-1 text-xs bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors"
                  >
                    <i class="fa fa-fire mr-1"></i>Hot
                  </button>

                  <!-- Favorite Button -->
                  <button
                    @click="openFavoriteModal(game)"
                    class="flex-1 px-2 py-1 text-xs bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors"
                  >
                    <i class="fa fa-star mr-1"></i>Favorite
                  </button>
                </div>
              </div>
            </div>

          </div>
        </div>

        <!-- Pagination -->
        <div class="flex items-center justify-between border-t border-gray-200 pt-4">
          <div class="text-sm text-gray-700">
            Showing {{ ((gamesParams.page - 1) * gamesParams.limit) + 1 }} to
            {{ Math.min(gamesParams.page * gamesParams.limit, gamesPagination.total) }} of
            {{ gamesPagination.total }} results
          </div>

          <div class="flex items-center space-x-2">
            <!-- Previous Button -->
            <button
              @click="changePage(gamesParams.page - 1)"
              :disabled="gamesParams.page <= 1"
              class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>

            <!-- Page Numbers -->
            <div class="flex space-x-1">
              <button
                v-for="page in visiblePages"
                :key="page"
                @click="changePage(page)"
                class="px-3 py-1 text-sm border rounded-md"
                :class="{
                  'bg-blue-600 text-white border-blue-600': page === gamesParams.page,
                  'border-gray-300 hover:bg-gray-50': page !== gamesParams.page
                }"
              >
                {{ page }}
              </button>
            </div>

            <!-- Next Button -->
            <button
              @click="changePage(gamesParams.page + 1)"
              :disabled="gamesParams.page >= gamesPagination.totalPages"
              class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>

      </div>

      <!-- No Games Message -->
      <div v-else class="text-center py-8 text-gray-500">
        <p>{{ selectedCategoryId ? 'No games available for this category.' : 'No games available.' }}</p>
      </div>
    </div>

    <!-- Category Modal -->
    <div v-if="showCategoryModal" class="fixed inset-0 bg-gray-800 bg-opacity-75 z-50 flex items-center justify-center p-4">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
        <!-- Header -->
        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4 flex justify-between items-center rounded-t-lg">
          <h2 class="text-lg font-bold text-white">{{ editingCategory ? 'Edit Category' : 'Add Category' }}</h2>
          <button @click="closeCategoryModal" class="text-white hover:text-gray-200 focus:outline-none">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Body -->
        <div class="p-6">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Category Name</label>
              <input
                v-model="categoryForm.category_name"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter category name"
              >
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                v-model="categoryForm.description"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter category description"
              ></textarea>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3 rounded-b-lg">
          <button
            @click="closeCategoryModal"
            class="px-4 py-2 text-sm bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
          >
            Cancel
          </button>
          <button
            @click="saveCategoryForm"
            :disabled="!categoryForm.category_name || categoryFormLoading"
            class="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            <span v-if="categoryFormLoading">Saving...</span>
            <span v-else>{{ editingCategory ? 'Update' : 'Create' }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Add Games Modal -->
    <div v-if="showAddGamesModal" class="fixed inset-0 bg-gray-800 bg-opacity-75 z-50 flex items-center justify-center p-4">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <!-- Header -->
        <div class="bg-gradient-to-r from-green-600 to-emerald-600 px-6 py-4 flex justify-between items-center">
          <h2 class="text-lg font-bold text-white">Add Games to Category</h2>
          <button @click="closeAddGamesModal" class="text-white hover:text-gray-200 focus:outline-none">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Body -->
        <div class="p-6 overflow-y-auto max-h-[70vh]">
          <!-- Category Selection -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">Assign to Category</label>
            <select
              v-model="addGamesForm.categoryId"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Choose a category...</option>
              <option v-for="category in categories" :key="category.id" :value="category.id">
                {{ category.category_name }}
              </option>
            </select>
          </div>

          <!-- Games Selection -->
          <div v-if="addGamesForm.categoryId">
            <div class="flex justify-between items-center mb-2">
              <label class="block text-sm font-medium text-gray-700">Select Games to Move</label>
              <span class="text-xs text-gray-500">{{ filteredAvailableGames.length }} of {{ availableGames.length }} games</span>
            </div>

            <!-- Search Input -->
            <div class="mb-3">
              <div class="relative">
                <input
                  v-model="gameSearchQuery"
                  type="text"
                  placeholder="Search games by name, provider, or ID..."
                  class="search-input w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                >
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <i class="fa fa-search text-gray-400"></i>
                </div>
                <button
                  v-if="gameSearchQuery"
                  @click="gameSearchQuery = ''"
                  class="search-clear-btn absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  <i class="fa fa-times"></i>
                </button>
              </div>
            </div>
            <div class="border border-gray-300 rounded-md max-h-60 overflow-y-auto">
              <!-- No Results Message -->
              <div v-if="filteredAvailableGames.length === 0 && gameSearchQuery" class="no-results p-4 text-center text-gray-500">
                <i class="fa fa-search text-2xl mb-2"></i>
                <p>No games found matching "{{ gameSearchQuery }}"</p>
                <button @click="gameSearchQuery = ''" class="text-blue-600 hover:text-blue-800 text-sm mt-1">
                  Clear search
                </button>
              </div>

              <!-- Games List -->
              <div
                v-for="game in filteredAvailableGames"
                :key="game.id"
                class="flex items-center p-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
              >
                <input
                  type="checkbox"
                  :value="game.id"
                  v-model="addGamesForm.gameIds"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3"
                >
                <div class="flex-1">
                  <div class="text-sm font-medium text-gray-900">{{ game.game_name || game.name }}</div>
                  <div class="text-xs text-gray-500">
                    {{ game.provider }} - ID: {{ game.id }}
                  </div>
                  <div class="text-xs mt-1">
                    <span class="text-gray-600">Current Category:</span>
                    <span
                      class="inline-flex px-2 py-1 text-xs rounded-full ml-1"
                      :class="{
                        'bg-blue-100 text-blue-800': game.category_name,
                        'bg-gray-100 text-gray-600': !game.category_name
                      }"
                    >
                      {{ game.category_name || 'No Category' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3">
          <button
            @click="closeAddGamesModal"
            class="px-4 py-2 text-sm bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
          >
            Cancel
          </button>
          <button
            @click="saveAddGamesForm"
            :disabled="!addGamesForm.categoryId || addGamesForm.categoryId === 'all' || addGamesForm.gameIds.length === 0 || addGamesFormLoading"
            class="px-4 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            <span v-if="addGamesFormLoading">Moving...</span>
            <span v-else>Move {{ addGamesForm.gameIds.length }} Games</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Game Details Modal -->
    <div v-if="showGameModal" class="fixed inset-0 bg-gray-800 bg-opacity-75 z-50 flex items-center justify-center p-4">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <!-- Header -->
        <div class="bg-gradient-to-r from-purple-600 to-indigo-600 px-6 py-4 flex justify-between items-center">
          <h2 class="text-lg font-bold text-white">
            {{ gameEditMode ? 'Edit Game' : 'Game Details' }}
          </h2>
          <div class="flex items-center space-x-2">
            <button
              v-if="!gameEditMode"
              @click="enableEditMode"
              class="text-white hover:text-gray-200 focus:outline-none px-3 py-1 bg-white bg-opacity-20 rounded-md transition-colors"
            >
              <i class="fa fa-edit mr-1"></i>Edit
            </button>
            <button @click="closeGameModal" class="text-white hover:text-gray-200 focus:outline-none">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>

        <!-- Body -->
        <div class="game-modal-content p-6">
          <div v-if="selectedGame" class="space-y-6">
            <!-- Basic Information -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">Game Name</label>
                  <input
                    v-if="gameEditMode"
                    v-model="gameEditForm.game_name"
                    type="text"
                    class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
                  >
                  <p v-else class="mt-1 text-sm text-gray-900">{{ selectedGame.game_name }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Provider</label>
                  <p class="mt-1 text-sm text-gray-900">{{ selectedGame.provider }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Game ID</label>
                  <p class="mt-1 text-sm text-gray-900">{{ selectedGame.id }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Status</label>
                  <select
                    v-if="gameEditMode"
                    v-model="gameEditForm.status"
                    class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
                  >
                    <option value="1">Active</option>
                    <option value="0">Suspended</option>
                  </select>
                  <span
                    v-else
                    class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1"
                    :class="{
                      'bg-green-100 text-green-800': selectedGame.status === '1',
                      'bg-red-100 text-red-800': selectedGame.status === '0'
                    }"
                  >
                    {{ selectedGame.status === '1' ? 'Active' : 'Suspended' }}
                  </span>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Transaction Count</label>
                  <p class="mt-1 text-sm text-gray-900">{{ selectedGame.trx_count || 'N/A' }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Created At</label>
                  <p class="mt-1 text-sm text-gray-900">{{ formatDate(selectedGame.created_at) }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Hot Value</label>
                  <input
                    v-if="gameEditMode"
                    v-model.number="gameEditForm.hot"
                    type="number"
                    min="0"
                    class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
                  >
                  <span
                    v-else
                    class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1"
                    :class="{
                      'bg-red-100 text-red-800': parseInt(selectedGame.hot) > 0,
                      'bg-gray-100 text-gray-800': parseInt(selectedGame.hot) === 0 || !selectedGame.hot
                    }"
                  >
                    {{ selectedGame.hot || 0 }}
                  </span>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Favorite Value</label>
                  <input
                    v-if="gameEditMode"
                    v-model.number="gameEditForm.favourite"
                    type="number"
                    min="0"
                    class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
                  >
                  <span
                    v-else
                    class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1"
                    :class="{
                      'bg-green-100 text-green-800': parseInt(selectedGame.favourite) > 0,
                      'bg-gray-100 text-gray-800': parseInt(selectedGame.favourite) === 0 || !selectedGame.favourite
                    }"
                  >
                    {{ selectedGame.favourite || 0 }}
                  </span>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Category</label>
                  <select
                    v-if="gameEditMode"
                    v-model="gameEditForm.category_id"
                    class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
                  >
                    <option value="">No Category</option>
                    <option v-for="category in categories" :key="category.id" :value="category.id">
                      {{ category.category_name }}
                    </option>
                  </select>
                  <span
                    v-else
                    class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1"
                    :class="{
                      'bg-yellow-100 text-yellow-800': selectedGame.favourite === '1',
                      'bg-gray-100 text-gray-800': selectedGame.favourite === '0'
                    }"
                  >
                    {{ selectedGame.category_name || 'No Category' }}
                  </span>
                </div>
              </div>

              <!-- Description -->
              <div v-if="selectedGame.description" class="mt-4">
                <label class="block text-sm font-medium text-gray-700">Description</label>
                <p class="mt-1 text-sm text-gray-900">{{ selectedGame.description }}</p>
              </div>

              <!-- Category -->
              <div v-if="selectedGame.category_name" class="mt-4">
                <label class="block text-sm font-medium text-gray-700">Category</label>
                <p class="mt-1 text-sm text-gray-900">{{ selectedGame.category_name }}</p>
              </div>

              <!-- Image URL -->
              <div v-if="selectedGame.image_url" class="mt-4">
                <label class="block text-sm font-medium text-gray-700">Image URL</label>
                <p class="mt-1 text-sm text-gray-900 break-all">{{ selectedGame.image_url }}</p>
              </div>
            </div>

            <!-- Extra Data -->
            <div v-if="selectedGame.extra_data" class="extra-data-section p-4 rounded-lg">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Extra Data</h3>
              <div v-if="parsedExtraData" class="space-y-3">
                <div v-for="(value, key) in parsedExtraData" :key="key" class="extra-data-item border-b border-blue-200 pb-2 last:border-b-0">
                  <label class="block text-sm font-medium text-blue-700 capitalize">{{ formatKey(key) }}</label>
                  <div class="mt-1">
                    <!-- Handle arrays -->
                    <div v-if="Array.isArray(value)" class="flex flex-wrap gap-1">
                      <span
                        v-for="item in value"
                        :key="item"
                        class="inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded"
                      >
                        {{ item }}
                      </span>
                    </div>
                    <!-- Handle objects -->
                    <div v-else-if="typeof value === 'object' && value !== null">
                      <pre class="text-xs text-blue-900 bg-blue-100 p-2 rounded overflow-x-auto">{{ JSON.stringify(value, null, 2) }}</pre>
                    </div>
                    <!-- Handle primitives -->
                    <p v-else class="text-sm text-blue-900">{{ value }}</p>
                  </div>
                </div>
              </div>
              <div v-else class="text-sm text-gray-600">
                <p class="mb-2">Raw Extra Data:</p>
                <pre class="text-xs bg-gray-100 p-3 rounded overflow-x-auto">{{ selectedGame.extra_data }}</pre>
              </div>
            </div>

            <!-- No Extra Data -->
            <div v-else class="bg-gray-50 p-4 rounded-lg text-center">
              <p class="text-gray-600">No extra data available for this game.</p>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3">
          <button
            v-if="gameEditMode"
            @click="cancelEdit"
            class="px-4 py-2 text-sm bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
          >
            Cancel
          </button>
          <button
            v-if="gameEditMode"
            @click="saveGameEdit"
            :disabled="gameEditLoading"
            class="px-4 py-2 text-sm bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            <span v-if="gameEditLoading">Saving...</span>
            <span v-else>Save Changes</span>
          </button>
          <button
            v-if="!gameEditMode"
            @click="closeGameModal"
            class="px-4 py-2 text-sm bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>

    <!-- Hot Value Modal -->
    <div v-if="showHotModal" class="fixed inset-0 bg-gray-800 bg-opacity-75 z-50 flex items-center justify-center p-4">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
        <!-- Header -->
        <div class="bg-gradient-to-r from-red-600 to-red-700 px-6 py-4 flex justify-between items-center rounded-t-lg">
          <h2 class="text-lg font-bold text-white">Set Hot Value</h2>
          <button @click="closeHotModal" class="text-white hover:text-gray-200 focus:outline-none">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Body -->
        <div class="p-6">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Game Name</label>
              <p class="text-sm text-gray-900 bg-gray-50 p-2 rounded">{{ selectedGameForUpdate?.game_name || selectedGameForUpdate?.name }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Hot Value (0 to ∞)</label>
              <input
                v-model.number="hotForm.value"
                type="number"
                min="0"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                placeholder="Enter hot value (0 = gray, 1+ = red)"
              >
              <p class="text-xs text-gray-500 mt-1">0 = Gray badge, 1 or more = Red badge</p>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3 rounded-b-lg">
          <button
            @click="closeHotModal"
            class="px-4 py-2 text-sm bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
          >
            Cancel
          </button>
          <button
            @click="saveHotValue"
            :disabled="hotFormLoading"
            class="px-4 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            <span v-if="hotFormLoading">Saving...</span>
            <span v-else>Save</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Favorite Value Modal -->
    <div v-if="showFavoriteModal" class="fixed inset-0 bg-gray-800 bg-opacity-75 z-50 flex items-center justify-center p-4">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
        <!-- Header -->
        <div class="bg-gradient-to-r from-green-600 to-green-700 px-6 py-4 flex justify-between items-center rounded-t-lg">
          <h2 class="text-lg font-bold text-white">Set Favorite Value</h2>
          <button @click="closeFavoriteModal" class="text-white hover:text-gray-200 focus:outline-none">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Body -->
        <div class="p-6">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Game Name</label>
              <p class="text-sm text-gray-900 bg-gray-50 p-2 rounded">{{ selectedGameForUpdate?.game_name || selectedGameForUpdate?.name }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Favorite Value (0 to ∞)</label>
              <input
                v-model.number="favoriteForm.value"
                type="number"
                min="0"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Enter favorite value (0 = gray, 1+ = green)"
              >
              <p class="text-xs text-gray-500 mt-1">0 = Gray badge, 1 or more = Green badge</p>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3 rounded-b-lg">
          <button
            @click="closeFavoriteModal"
            class="px-4 py-2 text-sm bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
          >
            Cancel
          </button>
          <button
            @click="saveFavoriteValue"
            :disabled="favoriteFormLoading"
            class="px-4 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            <span v-if="favoriteFormLoading">Saving...</span>
            <span v-else>Save</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  name: "BetsGamesCategories",
  components: {
    PageHeader
  },
  data() {
    return {
      isLoading: false,
      gamesLoading: false,
      selectedCategoryId: "",
      selectedGameIds: [],
      categories: [],
      games: [],
      availableGames: [],

      // Games Pagination
      gamesParams: {
        page: 1,
        limit: 100,
        category_id: ''
      },
      gamesPagination: {
        total: 0,
        totalPages: 0,
        currentPage: 1
      },

      // Filters
      filters: {
        game_name: '',
        provider: '',
        category_name: '',
        hot_min: null,
        hot_max: null,
        favourite_min: null,
        favourite_max: null
      },

      // Category Modal
      showCategoryModal: false,
      editingCategory: null,
      categoryForm: {
        category_name: '',
        description: ''
      },
      categoryFormLoading: false,

      // Add Games Modal
      showAddGamesModal: false,
      addGamesForm: {
        categoryId: '',
        gameIds: []
      },
      addGamesFormLoading: false,
      gameSearchQuery: '',

      // Game Details Modal
      showGameModal: false,
      selectedGame: null,
      gameEditMode: false,
      gameEditForm: {
        game_name: '',
        status: '',
        hot: '',
        favourite: '',
        category_id: ''
      },
      gameEditLoading: false,

      // Hot Modal
      showHotModal: false,
      selectedGameForUpdate: null,
      hotForm: {
        value: 0
      },
      hotFormLoading: false,

      // Favorite Modal
      showFavoriteModal: false,
      favoriteForm: {
        value: 0
      },
      favoriteFormLoading: false,
    }
  },
  async mounted() {
    await this.setCategories()
    await this.loadGames()
  },
  computed: {
    visiblePages() {
      const pages = []
      const totalPages = this.gamesPagination.totalPages
      const currentPage = this.gamesParams.page

      if (totalPages <= 7) {
        // Show all pages if 7 or fewer
        for (let i = 1; i <= totalPages; i++) {
          pages.push(i)
        }
      } else {
        // Show first page, current page area, and last page
        if (currentPage <= 4) {
          for (let i = 1; i <= 5; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(totalPages)
        } else if (currentPage >= totalPages - 3) {
          pages.push(1)
          pages.push('...')
          for (let i = totalPages - 4; i <= totalPages; i++) {
            pages.push(i)
          }
        } else {
          pages.push(1)
          pages.push('...')
          for (let i = currentPage - 1; i <= currentPage + 1; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(totalPages)
        }
      }

      return pages.filter(page => page !== '...' || pages.indexOf(page) === pages.lastIndexOf(page))
    },

    parsedExtraData() {
      if (!this.selectedGame || !this.selectedGame.extra_data) {
        return null
      }

      try {
        return JSON.parse(this.selectedGame.extra_data)
      } catch (error) {
        console.error('Error parsing extra_data:', error)
        return null
      }
    },

    filteredAvailableGames() {
      if (!this.gameSearchQuery) {
        return this.availableGames
      }

      const query = this.gameSearchQuery.toLowerCase()
      return this.availableGames.filter(game => {
        const gameName = (game.game_name || game.name || '').toLowerCase()
        const provider = (game.provider || '').toLowerCase()
        const gameId = (game.id || '').toString().toLowerCase()
        const categoryName = (game.category_name || '').toLowerCase()

        return gameName.includes(query) ||
               provider.includes(query) ||
               gameId.includes(query) ||
               categoryName.includes(query)
      })
    }
  },
  methods: {
    ...mapActions([
      "getGameCategories",
      "getGames",
      "updateGames",
      "createGameCategory",
      "updateGameCategory",
      "addGamesToCategory",
      "updateGameStatus",
      "toggleSideMenu"
    ]),

    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1);
    },

    async setCategories() {
      let app = this
      app.isLoading = true
      let params = new URLSearchParams({ timestamp: Date.now() });
      let response = await this.getGameCategories(params)
      if (response.status === 200) {
        app.categories = response.message.result || response.message || []
      } else {
        console.error("Error fetching categories:", response.message)
        app.categories = []
      }
      app.isLoading = false
    },

    selectCategory(categoryId) {
      this.selectedCategoryId = categoryId
      this.gamesParams.page = 1 // Reset to first page when changing category
      this.loadGames()
    },

    getGamesTitle() {
      if (!this.selectedCategoryId) {
        return 'All Games'
      } else {
        return 'Games in Category'
      }
    },

    // Filters Methods
    applyFilters() {
      console.log("Applying filters:", this.filters);
      // Add logic to apply filters to the games list
    },

    resetFilters() {
      this.filters = {
        game_name: '',
        provider: '',
        category_name: '',
        hot_min: null,
        hot_max: null,
        favourite_min: null,
        favourite_max: null
      };
      console.log("Filters reset");
      // Add logic to reset filters and reload the games list
    },

    // Category Modal Methods
    openCategoryModal(category = null) {
      this.editingCategory = category
      if (category) {
        this.categoryForm = {
          category_name: category.category_name || '',
          description: category.description || ''
        }
      } else {
        this.categoryForm = {
          category_name: '',
          description: ''
        }
      }
      this.showCategoryModal = true
    },

    closeCategoryModal() {
      this.showCategoryModal = false
      this.editingCategory = null
      this.categoryForm = {
        category_name: '',
        description: ''
      }
    },

    async saveCategoryForm() {
      if (!this.categoryForm.category_name) {
        this.$swal.fire('Error!', 'Category name is required.', 'error');
        return
      }

      let app = this
      app.categoryFormLoading = true

      const payload = {
        category_name: this.categoryForm.category_name,
        description: this.categoryForm.description,
        timestamp: Date.now()
      }

      try {
        let response
        if (this.editingCategory) {
          payload.id = this.editingCategory.id
          response = await this.updateGameCategory(payload)
        } else {
          response = await this.createGameCategory(payload)
        }

        if (response.status === 200) {
          app.$swal.fire({
            title: 'Success!',
            text: `Category ${this.editingCategory ? 'updated' : 'created'} successfully.`,
            icon: 'success'
          });
          app.closeCategoryModal()
          await app.setCategories()
        } else {
          app.$swal.fire('Error!', response.message || 'Failed to save category.', 'error');
        }
      } catch (error) {
        console.error("Error saving category:", error)
        app.$swal.fire('Error!', 'An error occurred while saving category.', 'error');
      }

      app.categoryFormLoading = false
    },

    async loadGames() {
      let app = this
      app.gamesLoading = true
      app.selectedGameIds = []

      // Update params with current pagination settings
      this.gamesParams.category_id = this.selectedCategoryId

      let params = new URLSearchParams({
        timestamp: Date.now(),
        limit: this.gamesParams.limit,
        page: this.gamesParams.page
      });

      // Only add category_id if a specific category is selected (not "all" or empty)
      if (this.selectedCategoryId && this.selectedCategoryId !== 'all') {
        params.append('category_id', this.selectedCategoryId)
      }

      let response = await this.getGames(params)
      if (response.status === 200) {
        const data = response.message
        app.games = data.result || data.data || []

        // Update pagination info
        const recordCount = data.record_count || data.total || app.games.length
        app.gamesPagination = {
          total: recordCount,
          totalPages: Math.ceil(recordCount / this.gamesParams.limit),
          currentPage: this.gamesParams.page
        }
      } else {
        console.error("Error fetching games:", response.message)
        app.games = []
        app.gamesPagination = { total: 0, totalPages: 0, currentPage: 1 }
      }
      app.gamesLoading = false
    },

    toggleAllGames() {
      if (this.selectedGameIds.length === this.games.length) {
        this.selectedGameIds = []
      } else {
        this.selectedGameIds = this.games.map(game => game.id)
      }
    },

    // Pagination Methods
    changePage(page) {
      if (page >= 1 && page <= this.gamesPagination.totalPages && page !== this.gamesParams.page) {
        this.gamesParams.page = page
        this.loadGames()
      }
    },

    // Game Status Toggle
    async toggleGameStatus(game) {
      const newStatus = game.status === "1" ? "0" : "1"
      const statusText = newStatus === "1" ? 'Active' : 'Suspended'

      const result = await this.$swal.fire({
        title: 'Change Game Status',
        text: `Are you sure you want to change "${game.game_name || game.name}" status to ${statusText}?`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: newStatus === "1" ? '#10b981' : '#ef4444',
        cancelButtonColor: '#6b7280',
        confirmButtonText: `Yes, make it ${statusText}`,
        cancelButtonText: 'Cancel'
      })

      if (result.isConfirmed) {
        let app = this

        const payload = {
          game_id: game.id,
          status: newStatus,
          timestamp: Date.now()
        }

        try {
          let response = await this.updateGameStatus(payload)
          if (response.status === 200) {
            // Update the game status in the local array
            const gameIndex = app.games.findIndex(g => g.id === game.id)
            if (gameIndex !== -1) {
              app.games[gameIndex].status = newStatus
            }

            app.$swal.fire({
              title: 'Success!',
              text: `Game status changed to ${statusText} successfully.`,
              icon: 'success',
              timer: 2000,
              showConfirmButton: false
            });
          } else {
            app.$swal.fire('Error!', response.message || 'Failed to update game status.', 'error');
          }
        } catch (error) {
          console.error("Error updating game status:", error)
          app.$swal.fire('Error!', 'An error occurred while updating game status.', 'error');
        }
      }
    },

    // Add Games Modal Methods
    async openAddGamesModal() {
      this.addGamesForm = {
        categoryId: '', // Start with no category selected so user must choose
        gameIds: []
      }
      await this.loadAvailableGames()
      this.showAddGamesModal = true
    },

    closeAddGamesModal() {
      this.showAddGamesModal = false
      this.addGamesForm = {
        categoryId: '',
        gameIds: []
      }
      this.availableGames = []
      this.gameSearchQuery = ''
    },

    async loadAvailableGames() {
      let app = this
      let params = new URLSearchParams({
        timestamp: Date.now(),
        limit: 1000,
        page: 1
        // Don't filter by category to get ALL available games
      });

      let response = await this.getGames(params)
      if (response.status === 200) {
        // Get all games with their current category information
        app.availableGames = response.message.result || response.message || []
        console.log("Loaded available games:", app.availableGames.length)
      } else {
        console.error("Error fetching available games:", response.message)
        app.availableGames = []
      }
    },

    async saveAddGamesForm() {
      if (!this.addGamesForm.categoryId || this.addGamesForm.gameIds.length === 0) {
        this.$swal.fire('Error!', 'Please select a specific category and at least one game.', 'error');
        return
      }

      let app = this
      app.addGamesFormLoading = true

      const payload = {
        category_id: this.addGamesForm.categoryId,
        game_ids: this.addGamesForm.gameIds,
        timestamp: Date.now()
      }

      try {
        let response = await this.addGamesToCategory(payload)
        if (response.status === 200) {
          const categoryName = this.categories.find(c => c.id === this.addGamesForm.categoryId)?.category_name || 'selected category'
          app.$swal.fire({
            title: 'Success!',
            text: `Successfully moved ${this.addGamesForm.gameIds.length} games to "${categoryName}".`,
            icon: 'success'
          });
          app.closeAddGamesModal()
          // Refresh games list regardless of current filter
          await app.loadGames()
        } else {
          app.$swal.fire('Error!', response.message || 'Failed to move games to category.', 'error');
        }
      } catch (error) {
        console.error("Error moving games to category:", error)
        app.$swal.fire('Error!', 'An error occurred while moving games to category.', 'error');
      }

      app.addGamesFormLoading = false
    },

    async saveGamesToCategory() {
      if (!this.selectedCategoryId || this.selectedGameIds.length === 0) {
        this.$swal.fire('Error!', 'Please select games to save.', 'error');
        return
      }

      let app = this
      app.isLoading = true

      const payload = {
        category_id: this.selectedCategoryId,
        game_ids: this.selectedGameIds,
        timestamp: Date.now()
      }

      try {
        let response = await this.updateGames(payload)
        if (response.status === 200) {
          app.$swal.fire({
            title: 'Success!',
            text: `Successfully updated ${this.selectedGameIds.length} games in the category.`,
            icon: 'success'
          });
          app.selectedGameIds = []
          await app.loadGames()
        } else {
          app.$swal.fire('Error!', response.message || 'Failed to update games.', 'error');
        }
      } catch (error) {
        console.error("Error updating games:", error)
        app.$swal.fire('Error!', 'An error occurred while updating games.', 'error');
      }

      app.isLoading = false
    },

    // Game Modal Methods
    openGameModal(game) {
      this.selectedGame = game
      this.showGameModal = true
      this.gameEditMode = false
    },

    closeGameModal() {
      this.showGameModal = false
      this.selectedGame = null
      this.gameEditMode = false
      this.gameEditForm = {
        game_name: '',
        status: '',
        hot: '',
        favourite: '',
        category_id: ''
      }
    },

    enableEditMode() {
      this.gameEditMode = true
      this.gameEditForm = {
        game_name: this.selectedGame.game_name,
        status: this.selectedGame.status,
        hot: this.selectedGame.hot,
        favourite: this.selectedGame.favourite,
        category_id: this.selectedGame.category_id || ''
      }
    },

    cancelEdit() {
      this.gameEditMode = false
      this.gameEditForm = {
        game_name: '',
        status: '',
        hot: '',
        favourite: '',
        category_id: ''
      }
    },

    async saveGameEdit() {
      let app = this
      app.gameEditLoading = true

      const payload = {
        game_id: this.selectedGame.id,
        game_name: this.gameEditForm.game_name,
        status: parseInt(this.gameEditForm.status),
        hot: parseInt(this.gameEditForm.hot),
        favourite: parseInt(this.gameEditForm.favourite),
        category_id: this.gameEditForm.category_id || null,
        timestamp: Date.now()
      }

      try {
        let response = await this.updateGameStatus(payload)
        if (response.status === 200) {
          // Update the selected game with new values
          app.selectedGame.game_name = this.gameEditForm.game_name
          app.selectedGame.status = this.gameEditForm.status
          app.selectedGame.hot = this.gameEditForm.hot
          app.selectedGame.favourite = this.gameEditForm.favourite
          app.selectedGame.category_id = this.gameEditForm.category_id

          // Update the game in the games array
          const gameIndex = app.games.findIndex(g => g.id === this.selectedGame.id)
          if (gameIndex !== -1) {
            app.games[gameIndex] = { ...app.games[gameIndex], ...app.selectedGame }
          }

          app.$swal.fire({
            title: 'Success!',
            text: 'Game updated successfully.',
            icon: 'success',
            timer: 2000,
            showConfirmButton: false
          });

          app.gameEditMode = false
        } else {
          app.$swal.fire('Error!', response.message || 'Failed to update game.', 'error');
        }
      } catch (error) {
        console.error("Error updating game:", error)
        app.$swal.fire('Error!', 'An error occurred while updating game.', 'error');
      }

      app.gameEditLoading = false
    },

    // Hot Modal Methods
    openHotModal(game) {
      this.selectedGameForUpdate = game
      this.hotForm.value = parseInt(game.hot) || 0
      this.showHotModal = true
    },

    closeHotModal() {
      this.showHotModal = false
      this.selectedGameForUpdate = null
      this.hotForm.value = 0
    },

    async saveHotValue() {
      if (!this.selectedGameForUpdate) return

      let app = this
      app.hotFormLoading = true

      const payload = {
        game_id: this.selectedGameForUpdate.id,
        hot: parseInt(this.hotForm.value) || 0,
        timestamp: Date.now()
      }

      try {
        let response = await this.updateGameStatus(payload)
        if (response.status === 200) {
          // Update the game in the local array
          const gameIndex = app.games.findIndex(g => g.id === this.selectedGameForUpdate.id)
          if (gameIndex !== -1) {
            app.games[gameIndex].hot = this.hotForm.value.toString()
          }

          app.$swal.fire({
            title: 'Success!',
            text: 'Hot value updated successfully.',
            icon: 'success',
            timer: 2000,
            showConfirmButton: false
          });

          app.closeHotModal()
        } else {
          app.$swal.fire('Error!', response.message || 'Failed to update hot value.', 'error');
        }
      } catch (error) {
        console.error("Error updating hot value:", error)
        app.$swal.fire('Error!', 'An error occurred while updating hot value.', 'error');
      }

      app.hotFormLoading = false
    },

    // Favorite Modal Methods
    openFavoriteModal(game) {
      this.selectedGameForUpdate = game
      this.favoriteForm.value = parseInt(game.favourite) || 0
      this.showFavoriteModal = true
    },

    closeFavoriteModal() {
      this.showFavoriteModal = false
      this.selectedGameForUpdate = null
      this.favoriteForm.value = 0
    },

    async saveFavoriteValue() {
      if (!this.selectedGameForUpdate) return

      let app = this
      app.favoriteFormLoading = true

      const payload = {
        game_id: this.selectedGameForUpdate.id,
        favourite: parseInt(this.favoriteForm.value) || 0,
        timestamp: Date.now()
      }

      try {
        let response = await this.updateGameStatus(payload)
        if (response.status === 200) {
          // Update the game in the local array
          const gameIndex = app.games.findIndex(g => g.id === this.selectedGameForUpdate.id)
          if (gameIndex !== -1) {
            app.games[gameIndex].favourite = this.favoriteForm.value.toString()
          }

          app.$swal.fire({
            title: 'Success!',
            text: 'Favorite value updated successfully.',
            icon: 'success',
            timer: 2000,
            showConfirmButton: false
          });

          app.closeFavoriteModal()
        } else {
          app.$swal.fire('Error!', response.message || 'Failed to update favorite value.', 'error');
        }
      } catch (error) {
        console.error("Error updating favorite value:", error)
        app.$swal.fire('Error!', 'An error occurred while updating favorite value.', 'error');
      }

      app.favoriteFormLoading = false
    },

    // Utility Methods
    formatDate(dateString) {
      if (!dateString) return 'N/A'
      try {
        return new Date(dateString).toLocaleString()
      } catch (error) {
        return dateString
      }
    },

    formatKey(key) {
      // Convert camelCase or snake_case to readable format
      return key
        .replace(/([A-Z])/g, ' $1')
        .replace(/_/g, ' ')
        .replace(/\b\w/g, l => l.toUpperCase())
        .trim()
    },
  }
}
</script>

<style scoped>
/* Category Cards Scrollbar */
.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Category Card Hover Effects */
.category-card {
  transition: all 0.2s ease-in-out;
}

.category-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Modal Animations */
.modal-enter-active, .modal-leave-active {
  transition: opacity 0.3s;
}

.modal-enter, .modal-leave-to {
  opacity: 0;
}

/* Table Hover Effects */
tbody tr:hover {
  background-color: #f8fafc;
}

/* Button Hover Effects */
button {
  transition: all 0.2s ease-in-out;
}

button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Game Card Styles */
.game-card {
  transition: all 0.2s ease-in-out;
}

.game-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Toggle Switch Styles */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #10b981;
}

input:checked + .slider:before {
  transform: translateX(20px);
}

/* Pagination Styles */
.pagination-button {
  transition: all 0.2s ease-in-out;
}

.pagination-button:hover:not(:disabled) {
  background-color: #f3f4f6;
  transform: none;
  box-shadow: none;
}

.pagination-button.active {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.pagination-button.active:hover {
  background-color: #2563eb;
}

/* Game Modal Styles */
.game-modal-content {
  max-height: 75vh;
  overflow-y: auto;
}

.game-modal-content::-webkit-scrollbar {
  width: 6px;
}

.game-modal-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.game-modal-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.game-modal-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Extra Data Styling */
.extra-data-section {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.extra-data-item {
  transition: all 0.2s ease-in-out;
}

.extra-data-item:hover {
  background-color: rgba(59, 130, 246, 0.05);
  border-radius: 4px;
  padding: 8px;
  margin: -4px;
}

/* View Button Styles */
.view-button {
  transition: all 0.2s ease-in-out;
}

.view-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

/* Edit Form Styles */
.edit-form-input {
  transition: all 0.2s ease-in-out;
}

.edit-form-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(147, 51, 234, 0.1);
}

/* Edit Mode Styles */
.edit-mode-header {
  background: linear-gradient(135deg, #7c3aed 0%, #3b82f6 100%);
}

.edit-button {
  transition: all 0.2s ease-in-out;
}

.edit-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

/* Category Badge Styles */
.category-badge {
  transition: all 0.2s ease-in-out;
}

.category-badge:hover {
  transform: scale(1.05);
}

.category-badge.has-category {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 1px solid #93c5fd;
}

.category-badge.no-category {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border: 1px solid #d1d5db;
}

/* Search Input Styles */
.search-input {
  transition: all 0.2s ease-in-out;
}

.search-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.search-clear-btn {
  transition: all 0.2s ease-in-out;
}

.search-clear-btn:hover {
  transform: scale(1.1);
}

/* No Results Styles */
.no-results {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
