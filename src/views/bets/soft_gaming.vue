<template>
  <div class="p-4">
    <!-- <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" /> -->
    <page-header pageName="Soft Gaming" pageSubtitle=" Bets" />

    <!-- Filter Section -->
    <div class="bg-white rounded-lg shadow-lg mb-4">
      <div class="p-4 border-b">
        <h3 class="text-lg font-medium text-gray-700">Filters</h3>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4">
        <!-- Bet Information Group -->
        <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-1">Bet Information</h4>
          <div class="space-y-2">
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">Bet ID</label>
              <input type="text" v-model="filterParams.bet_id" placeholder="Enter bet ID"
                class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                @keyup.enter="applyFilters()">
            </div>
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">Bet Reference</label>
              <input type="text" v-model="filterParams.bet_reference" placeholder="Enter bet reference"
                class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                @keyup.enter="applyFilters()">
            </div>
          </div>
        </div>

        <!-- Customer & IP Group -->
        <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-1">Customer & IP</h4>
          <div class="space-y-2">
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">Customer</label>
              <input type="text" v-model="filterParams.customer" placeholder="Enter customer phone"
                class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                @keyup.enter="applyFilters()">
            </div>
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">IP Address</label>
              <input type="text" v-model="filterParams.ip_address" placeholder="Enter customer IP address"
                class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                @keyup.enter="applyFilters()">
            </div>
          </div>
        </div>

        <!-- Bet Details Group -->
        <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-1">Bet Details</h4>
          <div class="space-y-2">
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">Game Name</label>
              <input type="text" v-model="filterParams.created_by" placeholder="Enter game name"
                class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                @keyup.enter="applyFilters()">
            </div>
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">Bet Type</label>
              <select v-model="filterParams.bet_type"
                class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                <option value="">All Types</option>
                <option value="0">Cash Bet</option>
                <option value="1">Bonus Bet</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Odds & Win Group -->
        <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-1">Odds & Win</h4>
          <div class="space-y-2">
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">Min Odd</label>
              <input type="number" v-model="filterParams.min_total_odd" placeholder="Min odd"
                class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                @keyup.enter="applyFilters()">
            </div>
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">Max Odd</label>
              <input type="number" v-model="filterParams.max_total_odd" placeholder="Max odd"
                class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                @keyup.enter="applyFilters()">
            </div>
          </div>
        </div>

        <!-- Amount & Status Group -->
        <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-1">Amount & Status</h4>
          <div class="space-y-2">
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">Min Bet Amount</label>
              <input type="number" v-model="filterParams.min_bet_amount" placeholder="Min amount"
                class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                @keyup.enter="applyFilters()">
            </div>
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">Max Bet Amount</label>
              <input type="number" v-model="filterParams.max_bet_amount" placeholder="Max amount"
                class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                @keyup.enter="applyFilters()">
            </div>
          </div>
        </div>

        <!-- Possible Win Group -->
        <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-1">Possible Win</h4>
          <div class="space-y-2">
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">Min Possible Win</label>
              <input type="number" v-model="filterParams.min_possible_win" placeholder="Min possible win"
                class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                @keyup.enter="applyFilters()">
            </div>
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">Max Possible Win</label>
              <input type="number" v-model="filterParams.max_possible_win" placeholder="Max possible win"
                class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                @keyup.enter="applyFilters()">
            </div>
          </div>
        </div>

        <!-- Status & Date Group -->
        <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-1">Status & Date</h4>
          <div class="space-y-2">
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">Status</label>
              <select v-model="filterParams.status"
                class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                <option value="">All Status</option>
                <option value="1">Won</option>
                <option value="3">Lost</option>
                <option value="0">Cancelled</option>
              </select>
            </div>
            <div>
              <label class="block text-xs text-gray-600 mb-1">Date Range</label>
              <VueDatePicker v-model="date" range multi-calendars :enable-time-picker="false" :format="'yyyy-MM-dd'"
                :preset-ranges="presetRanges" placeholder="Select date range" class="w-full text-xs"
                @change="selectDate" @update:model-value="selectDate" />
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-1">Actions</h4>
          <div class="space-y-2 pt-2">
            <button @click="applyFilters()"
              class="w-full px-3 py-2 bg-indigo-600 text-white text-xs rounded-md hover:bg-indigo-700 transition-colors">
              Apply Filters
            </button>
            <button @click="resetFilters()"
              class="w-full px-3 py-2 bg-gray-200 text-gray-700 text-xs rounded-md hover:bg-gray-300 transition-colors">
              Reset Filters
            </button>
          </div>
        </div>
      </div>
    </div>


    <!-- Soft Gaming Table using AutoTable component -->
    <auto-table
      :data="dataList"
      :loading="isLoading"
      :has-actions="false"
      :pagination="true"
      :server-side-pagination="true"
      :total-items="total"
      :items-per-page="limit"
      :current-page-prop="offset"
      :items-per-page-options="[10, 25, 50, 100]"
      :show-items-count="true"
      :decimal-places="decimalPlaces"
      :exclude-columns="excludeColumns"
      :header-styles="headerStyles"
      :column-styles="columnStyles"
      @page-change="handlePageChange"
      @items-per-page-change="handleLimitChange"
    >

    <template #customer="{ item }">
        <div>{{ item.customer }}</div>
        {{ item.msisdn }}
        {{ item.ip_address }}
    </template>

    <template #created_by="{ item }">
        <strong class="badge bg-indigo-500 text-white">
          <template v-if="item.created_by && item.created_by.includes(':') && item.created_by.split(':')[1]">
            {{ item.created_by.split(':')[1].charAt(0).toUpperCase() + item.created_by.split(':')[1].slice(1) }}
          </template>
          <template v-else-if="item.created_by && item.created_by.includes(' ') && item.created_by.split(' ')[0]">
            {{ item.created_by.split(' ')[0].charAt(0).toUpperCase() + item.created_by.split(' ')[0].slice(1) }}
          </template>
          <template v-else>
            {{ item.created_by || 'N/A' }}
          </template>
        </strong>
    </template>

    <template #bet_type="{ item }">
        <strong class="badge" :class="item.bet_type==0?'bg-cyan-500 text-white':'bg-indigo-500 text-white'">
          {{ item.bet_type==0?'Cash Bet':'Bonus Bet' }}</strong>
    </template>

    <template #statusj="{ item }">
        <strong class="badge" :class="item.status==1?'bg-green-500 text-white':item.status=='3'?'bg-red-500 text-white':'bg-gray-500 text-white'">
          {{ item.status=='1'?'Won':item.status=='3'?'Lost':'Cancelled' }}</strong>
    </template>

    <template #status="{ item }">
      <strong 
        class="badge" 
        :class="item.status == '1' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'"
      >
        {{ item.status == '1' ? 'Won' : 'Lost' }}
      </strong>
    </template>


    </auto-table>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import AutoTable from '@/components/common/AutoTable.vue';
import PageHeader from '@/components/common/PageHeader.vue';
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';
import moment from "moment";

export default {
  name: "SoftGaming",
  components: {
    AutoTable,
    PageHeader,
    VueDatePicker
  },
  data() {
    return {
      isLoading: false,
      total: 0,
      limit: 100,
      offset: 1,
      moreParams: {
        page: 1,
        limit: 100,
        timestamp: Date.now(),
        skip_cache: false,
      },
      dataList: [],
      decimalPlaces: {
        // total_odd: 0,
      },
      excludeColumns: [
        'trx_count',
        'provider_name',
        'client_id',
        'bet_currency',
        'total_games',
        'extra_data',
        'msisdn',
        'internal_game_id',
        'ip_address',
        'profile_id',
        'excise_tax',
        'witholding_tax',
        'kra_report',
        'bet_transaction_id',
        'bet_credit_transaction_id',
      ],
      columnStyles: {
        total_odd: 'text-left font-bold text-purple-700',
        // created_at: 'text-left font-bold text-purple-700 w-1/4',
      },
      headerStyles: {
        // registration
        bet_reference: 'text-left font-bold text-black-700 bg-purple-50 text-xs w-1/4',
        created_at: 'text-left font-bold text-black-700 bg-purple-50 text-xs w-1/4',
      },
      // Filter parameters
      filterParams: {
        bet_id: '',
        bet_reference: '',
        customer: '',
        ip_address: '',
        created_by: '',
        bet_type: '',
        min_bet_amount: '',
        max_bet_amount: '',
        min_total_odd: '',
        max_total_odd: '',
        min_possible_win: '',
        max_possible_win: '',
        status: ''
      },
      // Date picker
      date: null,
      presetRanges: [
        { label: 'Today', range: [new Date(), new Date()] },
        { label: 'Yesterday', range: [new Date(Date.now() - 24 * 60 * 60 * 1000), new Date(Date.now() - 24 * 60 * 60 * 1000)] },
        { label: 'This week', range: [new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), new Date()] },
        { label: 'Last 7 days', range: [new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), new Date()] },
        { label: 'Last 30 days', range: [new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), new Date()] },
        { label: 'This month', range: [new Date(new Date().getFullYear(), new Date().getMonth(), 1), new Date()] },
        { label: 'Last month', range: [new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1), new Date(new Date().getFullYear(), new Date().getMonth(), 0)] }
      ],
    }
  },
  async mounted() {
    await this.setSoftGaming()
  },
  methods: {
    ...mapActions(["getSoftGaming", "toggleSideMenu"]),
    
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    shouldShowLost(item) {
    const isStatusZero = item.status === '0';
    if (!isStatusZero) return false;

    // Check if `created_at` is older than 3 minutes
    const createdAt = new Date(item.created_at);
    const threeMinutesAgo = new Date(Date.now() - 3 * 60 * 1000); // 3 mins ago
    
    return createdAt <= threeMinutesAgo;
  },
  
    async setSoftGaming() {
      let app = this
      app.isLoading = true
      let params = new URLSearchParams(app.moreParams);
      // console.log("Params: " + params.toString());
      
      let response = await this.getSoftGaming(params)
      if (response.status === 200) {
        app.dataList = response.message.result || response.message

        // Set total count for pagination
        if (response.message.record_count) {
          app.total = parseInt(response.message.record_count)
        } else if (response.message.total_count) {
          app.total = parseInt(response.message.total_count)
        } else {
          app.total = app.dataList.length
        }
      } else {
        console.error("Error fetching soft gaming data:", response.message)
        app.dataList = []
        app.total = 0
      }

      app.isLoading = false
    },

    async handlePageChange(page) {
      this.offset = page
      this.moreParams.page = page
      await this.setSoftGaming()
    },

    async handleLimitChange(newLimit) {
      this.limit = newLimit
      this.moreParams.limit = newLimit
      this.offset = 1
      this.moreParams.page = 1
      await this.setSoftGaming()
    },

    // Filter methods
    applyFilters() {
      // Reset to first page when applying filters
      this.offset = 1
      this.moreParams.page = 1

      // Add filter parameters to moreParams
      Object.keys(this.filterParams).forEach(key => {
        if (this.filterParams[key] !== '' && this.filterParams[key] !== null) {
          this.moreParams[key] = this.filterParams[key]
        } else {
          delete this.moreParams[key]
        }
      })

      // Add date range if selected
      if (this.date && Array.isArray(this.date) && this.date.length === 2) {
        this.moreParams.start_date = this.formatDate(this.date[0])
        this.moreParams.end_date = this.formatDate(this.date[1])
      } else {
        delete this.moreParams.start_date
        delete this.moreParams.end_date
      }

      // Update timestamp to force cache refresh
      this.moreParams.timestamp = Date.now()

      // Fetch data with filters
      this.setSoftGaming()
    },

    resetFilters() {
      // Reset all filter parameters
      this.filterParams = {
        bet_id: '',
        bet_reference: '',
        customer: '',
        ip_address: '',
        created_by: '',
        bet_type: '',
        min_bet_amount: '',
        max_bet_amount: '',
        min_total_odd: '',
        max_total_odd: '',
        min_possible_win: '',
        max_possible_win: '',
        status: ''
      }

      // Reset date
      this.date = null

      // Reset pagination
      this.offset = 1
      this.moreParams = {
        page: 1,
        limit: this.limit,
        timestamp: Date.now(),
        skip_cache: false,
      }

      // Fetch data without filters
      this.setSoftGaming()
    },

    selectDate() {
      // This method is called when date is selected
      // The actual filtering happens in applyFilters()
    },

    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },
  }
}
</script>

<style scoped>
/* Filter card styles */
.filter-card {
  transition: all 0.2s ease-in-out;
}

.filter-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Custom input styles for smaller size */
.filter-card input,
.filter-card select {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
}

/* Date picker custom styling */
.dp__input {
  font-size: 0.75rem !important;
  padding: 0.375rem 0.75rem !important;
}
</style>
