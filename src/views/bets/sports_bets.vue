<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Sports" pageSubtitle="Bets" />


    <!-- Main Bets Table -->
    <div class="transition-all duration-300 ease-in-out">

      <!-- Filter Section -->
      <div class="px-4 mb-5">
        <div class="bg-white rounded-lg shadow p-4">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-700">Filters</h3>
            <div class="flex gap-1">
              <button @click="applyFilters()"
                class="px-3 py-1 text-sm bg-indigo-600 text-white rounded-md hover:bg-indigo-700 flex items-center">
                <i class="fas fa-search mr-1"></i> Search
              </button>
              <button @click="resetFilters()"
                class="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
                Reset
              </button>
            </div>
          </div>

          <!-- First row of filters - 3 groups -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mb-3">
            <!-- User Identification -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="font-medium text-gray-700 mb-1 text-sm">User Identification</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Phone Number</label>
                  <input type="number" placeholder="Enter phone number"
                    class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="moreParams.mobile_number" @keyup.enter="setBets()">
                </div>
                <div>
                  <label class="block text-xs text-gray-600 mb-1">IP Address</label>
                  <input type="text" placeholder="Enter IP address"
                    class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="moreParams.ip" @keyup.enter="setBets()">
                </div>
              </div>
            </div>

            <!-- Bet Identification -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="font-medium text-gray-700 mb-1 text-sm">Bet Identification</h4>
              <div class="space-y-2">
                <!-- <div>
                  <label class="block text-xs text-gray-600 mb-1">Bet Reference</label>
                  <input type="text" placeholder="Enter bet reference"
                    class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="moreParams.bet_reference" @keyup.enter="setBets()">
                </div> -->

                <div>
                  <label class="block text-xs text-gray-600 mb-1">Match ID</label>
                  <input type="text" placeholder="Enter match ID"
                    class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="moreParams.match_id" @keyup.enter="setBets()">
                </div>

                <div>
                  <label class="block text-xs text-gray-600 mb-1">Bet ID</label>
                  <input type="text" placeholder="Enter bet ID"
                    class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="moreParams.bet_id" @keyup.enter="setBets()">
                </div>
              </div>
            </div>

            <!-- Amount Ranges -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="font-medium text-gray-700 mb-1 text-sm">Amount Ranges</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Bet Amount</label>
                  <div class="flex gap-1">
                    <input type="number" placeholder="Min"
                      class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                      v-model="moreParams.stake_amount_min" @keyup.enter="setBets()">
                    <input type="number" placeholder="Max"
                      class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                      v-model="moreParams.stake_amount_max" @keyup.enter="setBets()">
                  </div>
                </div>
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Possible Win</label>
                  <div class="flex gap-1">
                    <input type="number" placeholder="Min"
                      class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                      v-model="moreParams.winning_amount_min" @keyup.enter="setBets()">
                    <input type="number" placeholder="Max"
                      class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                      v-model="moreParams.winning_amount_max" @keyup.enter="setBets()">
                  </div>
                </div>
              </div>
            </div>


          </div>

          <!-- Second row of filters - 3 groups -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            <!-- Bet Details -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="font-medium text-gray-700 mb-1 text-sm">Bet Details</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Odds</label>
                  <div class="flex gap-1">
                    <input type="number" placeholder="Min"
                      class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                      v-model="moreParams.odds_min" @keyup.enter="setBets()">
                    <input type="number" placeholder="Max"
                      class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                      v-model="moreParams.odds_max" @keyup.enter="setBets()">
                  </div>
                </div>
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Selections</label>
                  <div class="flex gap-1">
                    <input type="number" placeholder="Min"
                      class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                      v-model="moreParams.selections_min" @keyup.enter="setBets()">
                    <input type="number" placeholder="Max"
                      class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                      v-model="moreParams.selections_max" @keyup.enter="setBets()">
                  </div>
                </div>
              </div>
            </div>

            <!-- Date & Status -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="font-medium text-gray-700 mb-1 text-sm">Date & Status</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Bets Placed On</label>
                  <VueDatePicker v-model="date" range :preset-ranges="presetRanges" position="center" :clearable="false"
                    :enable-time-picker="false" @closed="selectDate" class="w-full text-xs">
                    <template #yearly="{ label, range, presetDateRange }">
                      <span @click="presetDateRange(range)">{{ label }}</span>
                    </template>
                  </VueDatePicker>
                </div>
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Bet Status</label>
                  <select
                    class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="selected_bet_status">
                    <!-- <option value="" disabled selected>Select Bet Status</option> -->
                    <option v-for="type in bet_statuses" :value="type.value">
                      {{ type.text }}
                    </option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Bet Types -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="font-medium text-gray-700 mb-1 text-sm">Bet Types</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Bet Placement Type</label>
                  <select
                    class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="selected_placement_type">
                    <!-- <option value="" disabled selected>Select Type</option> -->
                    <option v-for="type in bet_placement_types" :value="type.value">
                      {{ type.text }}
                    </option>
                  </select>
                </div>
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Selection Type</label>
                  <select
                    class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="selected_selection_type">
                    <!-- <option value="" disabled selected>Select Selection Type</option> -->
                    <option v-for="type in selection_types" :value="type.value">
                      {{ type.text }}
                    </option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Game Options -->
            <!-- <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
            <h4 class="font-medium text-gray-700 mb-1 text-sm">Game Options</h4>
            <div class="space-y-2">
              <div>
                <label class="block text-xs text-gray-600 mb-1">Game Type</label>
                <select class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500" v-model="selected_game_type">
                  <option value="" disabled selected>Select Game Type</option>
                  <option v-for="type in game_types" :value="type.value">
                    {{ type.name }}
                  </option>
                </select>
              </div>
              <div>
                <label class="block text-xs text-gray-600 mb-1">Live Status</label>
                <select class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500" v-model="selected_is_live">
                  <option value="" disabled selected>Select Live Status</option>
                  <option v-for="type in is_live_types" :value="type.value">
                    {{ type.text }}
                  </option>
                </select>
              </div>
            </div>
          </div> -->

            <!-- Additional Filters -->
            <!-- <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
            <h4 class="font-medium text-gray-700 mb-1 text-sm">Additional Filters</h4>
            <div class="space-y-2">
              <div>
                <label class="block text-xs text-gray-600 mb-1">Profile ID</label>
                <input type="text" placeholder="Enter profile ID"
                       class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                       v-model="moreParams.profile_id"
                       @keyup.enter="setBets()">
              </div>
              <div>
                <label class="block text-xs text-gray-600 mb-1">Sort By</label>
                <select class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                        v-model="moreParams.sort"
                        @change="setBets()">
                  <option value="">Default</option>
                  <option value="date_asc">Date (Oldest)</option>
                  <option value="date_desc">Date (Newest)</option>
                  <option value="amount_asc">Amount (Low-High)</option>
                  <option value="amount_desc">Amount (High-Low)</option>
                </select>
              </div>
            </div>
          </div> -->

          </div>
        </div>
      </div>

      <!-- Summary Cards - Only show if there is data -->
      <div v-if="bets.length > 0" class="px-4 mb-5">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Total Bet Amount Card -->
          <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg p-4 text-white">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-blue-100 text-sm font-medium">Total Bet Amount</p>
                <p class="text-2xl font-bold">{{ formatNumberWithCommas(totalBetAmount) }}</p>
              </div>
              <div class="bg-blue-400 bg-opacity-30 rounded-full p-3">
                <i class="fas fa-coins text-xl"></i>
              </div>
            </div>
          </div>

          <!-- Total Odds Card -->
          <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow-lg p-4 text-white">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-purple-100 text-sm font-medium">Total Odds</p>
                <p class="text-2xl font-bold">{{ formatNumberWithCommas(totalOdds) }}</p>
              </div>
              <div class="bg-purple-400 bg-opacity-30 rounded-full p-3">
                <i class="fas fa-chart-line text-xl"></i>
              </div>
            </div>
          </div>

          <!-- Total Possible Win Card -->
          <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-lg p-4 text-white">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-green-100 text-sm font-medium">Total Possible Win</p>
                <p class="text-2xl font-bold">{{ formatNumberWithCommas(totalPossibleWin) }}</p>
              </div>
              <div class="bg-green-400 bg-opacity-30 rounded-full p-3">
                <i class="fas fa-trophy text-xl"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <auto-table class="mx-3"
      :headers="tableHeaders" :data="bets" :has-actions="false" :total-items="total" :items-per-page="limit"
        :current-page-prop="offset" :server-side-pagination="true" :pagination="total > limit" :show-items-count="true"
        :items-per-page-options="[10, 25, 50, 100]" :header-styles="headerStyles" :column-styles="columnStyles"
        @page-change="goToPage" @items-per-page-change="changeLimit">
        <!-- Table column templates -->
        <template #bet_id="{ item }">
          <div>{{ item.bet_id }}</div>
        </template>

        <template #bet_reference="{ item }">
          <div>
            <span style="font-size: 10px">{{ item.bet_reference }}</span>
            <br>
            <span style="font-size: 11px; font-weight: bold">BETID: {{ item.bet_id }}</span>
          </div>
        </template>

        <template #customer="{ item }">
          <div>
            <span>{{ item.mobile }}</span>
            <br>
            <span>{{ item.ip_address }}</span>
          </div>
        </template>

        <template #stake="{ item }">
          <div>{{ formatNumber(item.bet_amount) }}</div>
        </template>

        <template #possible_win="{ item }">
          <div>
            <strong>{{ formatNumber(item.possible_win) }}</strong>
          </div>
        </template>

        <template #bet_type="{ item }">
          <div class="bet-type-badge" :class="getBetTypeClass(item.bet_type)">
            {{ getBetTypeText(item.bet_type) }}
          </div>
        </template>

        <template #total_games="{ item }">
          <div>{{ item.total_games }}</div>
        </template>

        <template #total_odd="{ item }">
          <div>{{ formatNumber(item.total_odd) }}</div>
          <div>{{ item.total_games }} Game(s)</div>
        </template>

        <template #bet_status="{ item }">
          <div class="status-badge" :class="getStatusClass(item.bet_status, item)">
            {{ getStatusText(item.bet_status, item) }}
          </div>
        </template>

        <template #date="{ item }">
          <div>
            <span style="font-size: 11px; color: grey">{{ moment(item.created_at).format('llll') }}</span>
          </div>
        </template>

        <template #actions="{ item, index }">
          <div class="relative z-10">
            <action-dropdown v-if="(parseInt(item.match_status) !== 0) && (parseInt(item.match_status) !== null)"
              button-text="Actions" :show-text="false" button-class="z-50" menu-class="z-50 origin-top-right"
              :menu-width="48">
              <action-item text="View Bet Slip" color="blue" @click="viewBetSlip(item)">
                <template #icon>
                  <svg class="mr-3 h-5 w-5 text-blue-500 group-hover:text-blue-600" xmlns="http://www.w3.org/2000/svg"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </template>
              </action-item>

              <action-item text="Resettle Bet" color="blue" @click="resettleBet(item)">
                <template #icon>
                  <svg class="mr-3 h-5 w-5 text-blue-500 group-hover:text-blue-600" xmlns="http://www.w3.org/2000/svg"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </template>
              </action-item>

              <action-item text="Cancel Bet" color="blue" @click="cancelBet(item)">
                <template #icon>
                  <svg class="mr-3 h-5 w-5 text-red-500 group-hover:text-blue-600" xmlns="http://www.w3.org/2000/svg"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </template>
              </action-item>
            </action-dropdown>

            <button v-else class="px-3 py-1 flex items-center space-x-1">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                stroke="#808080" class="w-6 h-6">
                <path stroke-linecap="round" stroke-linejoin="round"
                  d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </button>
          </div>
        </template>


      </auto-table>

      <!-- Risk State & KRA Report Modal -->
      <div v-if="isRiskKraModalOpen"
        class="fixed inset-0 bg-gray-500 bg-opacity-50 z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden w-full max-w-2xl p-6">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold">Risk State & KRA Report</h3>
            <button @click="closeRiskKraModal" class="text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
          </div>

          <div v-if="selectedBetItem" class="space-y-4">
            <!-- Bet Information -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <h4 class="font-semibold text-gray-700 mb-2">Bet Information</h4>
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="font-medium">Bet ID:</span> {{ selectedBetItem.bet_id }}
                </div>
                <div>
                  <span class="font-medium">Bet Reference:</span> {{ selectedBetItem.bet_reference }}
                </div>
                <div>
                  <span class="font-medium">Customer:</span> {{ selectedBetItem.mobile }}
                </div>
                <div>
                  <span class="font-medium">Stake:</span> Ksh.{{ formatNumber(selectedBetItem.bet_amount) }}
                </div>
              </div>
            </div>

            <!-- Risk State -->
            <div class="bg-blue-50 p-4 rounded-lg">
              <h4 class="font-semibold text-gray-700 mb-2">Risk State</h4>
              <div class="flex items-center space-x-3">
                <div class="status-badge" :class="getRiskStateClass(selectedBetItem.risk_state)">
                  {{ getRiskStateText(selectedBetItem.risk_state) }}
                </div>
                <span class="text-sm text-gray-600">
                  Status: {{ selectedBetItem.risk_state === '1' 
                  ? 'Submitted to risk management' 
                  : 'Not submitted to risk management' }}
                </span>
              </div>
            </div>

            <!-- KRA Report -->
            <div class="bg-green-50 p-4 rounded-lg">
              <h4 class="font-semibold text-gray-700 mb-2">KRA Report</h4>
              <div class="flex items-center space-x-3">
                <div class="status-badge" :class="getKraReportClass(selectedBetItem.kra_report)">
                  {{ getKraReportText(selectedBetItem.kra_report) }}
                </div>
                <span class="text-sm text-gray-600">
                  Status: {{ selectedBetItem.kra_report === '1' ? 'Reported to KRA' : 'Not reported to KRA' }}
                </span>
              </div>
            </div>
          </div>

          <div class="mt-6 flex justify-end">
            <button @click="closeRiskKraModal" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
              Close
            </button>
          </div>
        </div>
      </div>

      <!-- Extra Data Modal -->
      <div v-if="isExtraDataModalOpen"
        class="fixed inset-0 bg-gray-500 bg-opacity-50 z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden w-full max-w-4xl p-6">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold">Extra Data</h3>
            <button @click="closeExtraDataModal" class="text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
          </div>

          <div v-if="selectedBetItem" class="space-y-4">
            <!-- Bet Information -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <h4 class="font-semibold text-gray-700 mb-2">Bet Information</h4>
              <div class="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span class="font-medium">Bet ID:</span> {{ selectedBetItem.bet_id }}
                </div>
                <div>
                  <span class="font-medium">Bet Reference:</span> {{ selectedBetItem.bet_reference }}
                </div>
                <div>
                  <span class="font-medium">Customer:</span> {{ selectedBetItem.mobile }}
                </div>
              </div>
            </div>

            <!-- Extra Data Content -->
            <div class="bg-blue-50 p-4 rounded-lg">
              <h4 class="font-semibold text-gray-700 mb-2">Extra Data</h4>
              <div v-if="selectedBetItem.extra_data" class="bg-white p-3 rounded border">
                <div v-if="getExtraDataKeyValuePairs(selectedBetItem.extra_data).length > 0" class="space-y-3">
                  <div v-for="(item, index) in getExtraDataKeyValuePairs(selectedBetItem.extra_data)" :key="index"
                    class="border-b border-gray-200 pb-2 last:border-b-0">
                    <div class="font-medium text-gray-800 text-sm mb-1">{{ item.key }}</div>
                    <div class="text-gray-600 text-sm">{{ item.value }}</div>
                  </div>
                </div>
                <div v-else class="text-gray-500 text-sm">
                  Unable to parse extra data.
                </div>
              </div>
              <div v-else class="text-gray-500 text-sm">
                No extra data available for this bet.
              </div>
            </div>
          </div>

          <div class="mt-6 flex justify-end">
            <button @click="closeExtraDataModal" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import moment from "moment-timezone";
import { AutoTable, ActionDropdown, ActionItem, CustomLoading } from '@/components/common';
import { startOfMonth, endOfMonth, startOfYear, endOfYear, subMonths } from "date-fns";
import VueDatePicker from "@vuepic/vue-datepicker";
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  components: {
    AutoTable,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    VueDatePicker,
    PageHeader,
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 100, // Set default limit
      offset: 1,
      checkboxes: {
        mobile_number: false,
        ip: false,
        winning_amount: false,
        odds: false,
        selections: false,
        bet_amount: false,
        bet_reference: false,
        bet_id: false,
      },
      showDropdown: [],
      viewModelOpen: false,
      isProcessingBetSlip: false,

      // Modal states
      isRiskKraModalOpen: false,
      isExtraDataModalOpen: false,
      selectedBetItem: null,

      tableHeaders: [
        { key: "customer", label: "Customer" },
        { key: "bet_reference", label: "Bet Reference", },
        { key: "stake", label: "Stake" },
        { key: "total_odd", label: "Total Odd" },
        { key: "possible_win", label: "Pos Win", },
        { key: "bet_type", label: "Bet Type", },
        { key: "bet_status", label: "Status", },
        { key: "date", label: "Date" },
        { key: "actions", label: "Actions" }
      ],

      headerStyles: {
        customer: 'text-center',
        bet_reference: 'text-center',
        stake: 'text-right',
        total_odd: 'text-right',
        possible_win: 'text-right',
        bet_type: 'text-center',
        bet_status: 'text-center',
        date: 'text-center',
        actions: 'text-center'
      },
      
      columnStyles: {
        customer: 'text-center',
        bet_reference: 'text-center',
        stake: 'text-center',
        total_odd: 'text-center',
        possible_win: 'text-center',
        bet_type: 'text-center',
        bet_status: 'text-center',
        date: 'text-center',
        actions: 'text-center'
      },

      rowHighlightConditions: {
        '0': 'bg-red-50',  // Lost bets with light red background
        '1': 'bg-green-50', // Won bets with light green background
        '2': 'bg-yellow-50' // Pending bets with light yellow background
      },
      //
      bets: [],
      //
      moreParams: {
        stake_amount_min: '',
        stake_amount_max: '',
        winning_amount_min: '',
        winning_amount_max: '',
        odds_min: '',
        odds_max: '',
        selections_min: '',
        selections_max: '',
        bet_type: '',
        selection_type: '',
        profile_id: '',
        ip: '',
        mobile_number: '',
        status: '',
        unsettled: '',
        sort: '',
        start: '',
        end: '',
        page: '',
        limit: "100", // This should match the limit property above
        timestamp: 'timestamp',
        skip_cache: '',
      },

      selected_game_type: { text: 'All', value: '' },
      game_types: [
        { text: 'All', value: '' },
        { name: 'sports', value: 'sports' },
        { name: 'Virtual', value: 'virtual' },
        { name: 'Instant', value: 'instant' },
        { name: 'Casino', value: 'casino' },
      ],

      selected_selection_type: { text: 'All', value: '' },
      selection_types: [
        { text: 'All', value: '' },
        { text: 'Single Bet', value: 1 },
        { text: 'Multi Bet', value: 2 },
      ],

      selected_placement_type: { text: 'All', value: '' },
      bet_placement_types: [
        { text: 'All', value: '' },
        { text: 'Cash Bet', value: 0 },
        { text: 'Bonus Bet', value: 1 },
        { text: 'Free Bet', value: 2 },
      ],

      selected_is_live: { text: 'All', value: '' },
      is_live_types: [
        { text: 'All', value: '' },
        { text: 'PreMatch', value: 1 },
        { text: 'Live', value: 2 },
      ],

      selected_bet_status: { text: 'All', value: '' },
      bet_statuses: [
        { text: 'All', value: '' },
        { text: 'Pending', value: 0 },
        { text: 'Won ', value: 1 },
        { text: 'Lost ', value: 3 },
        { text: 'Canceled ', value: 9 },
        { text: 'Unsettled ', value: 2 },
      ],

      date: null,
      presetRanges: [
        { label: 'Today', range: [new Date(), new Date()] },
        { label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())] },
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        { label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())] },
        {
          label: 'This year (slot)',
          range: [startOfYear(new Date()), endOfYear(new Date())],
          slot: 'yearly',
        },
      ],
    }
  },
  computed: {
    // Calculate total bet amount from all bets
    totalBetAmount() {
      return this.bets.reduce((total, bet) => {
        const amount = parseFloat(bet.bet_amount) || 0;
        return total + amount;
      }, 0);
    },

    // Calculate total odds from all bets
    totalOdds() {
      let sum = 0
      for (let i = 0; i < this.bets.length; i++) {
        sum += parseFloat(this.bets[i].total_odd)
      }
      return sum
    },

    // Calculate total possible win from all bets
    totalPossibleWin() {
      let sum = 0
      for (let i = 0; i < this.bets.length; i++) {
        sum += parseFloat(this.bets[i].possible_win)
      }
      return sum
    }
  },
  watch: {
    // watch if moreParams.bet_type changes
    selected_bet_status(newVal, oldVal) {
      if (newVal !== oldVal) {
        if (newVal === 2) {
          this.moreParams.unsettled = '2'
        } else {
          this.moreParams.status = newVal
          this.moreParams.unsettled = ''
        }
        this.setBets()
      }
    },

    // selected_selection_type
    selected_selection_type(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.moreParams.selection_type = newVal
        this.setBets()
      }
    },

    // selected_placement_type
    selected_placement_type(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.moreParams.bet_type = newVal
        this.setBets()
      }
    },

  },
  mounted() {
    this.setBets()
  },
  methods: {
    ...mapActions(["getSportsBets", "getSportsBetSlips", "fillBetDetails", "fillBet", "cancelCustomerBet", "resettleBetSlip", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    getRowActions(item) {
      const actions = [
        {
          label: 'View Bet Slip',
          action: () => this.viewBetSlip(item),
          icon: 'eye',
          class: 'text-blue-600'
        }
      ];

      // Add resettle action conditionally
      if (item.bet_status === '1' && item.bet_credit_transaction_id === null) {
        actions.push({
          label: 'Resettle Bet',
          action: () => this.resettleBet(item),
          icon: 'refresh',
          class: 'text-blue-600'
        });
      }

      actions.push(
        {
          label: 'View Risk State & KRA Report',
          action: () => this.openRiskKraModal(item),
          icon: 'document',
          class: 'text-purple-600'
        },
        {
          label: 'View Extras',
          action: () => this.openExtraDataModal(item),
          icon: 'info',
          class: 'text-green-600'
        }
      );

      return actions;
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    async selectDate() {
      let vm = this

      // If date is null (cleared), reset date filters and fetch data
      if (!this.date) {
        console.log('Date filter cleared, resetting and fetching data...');
        vm.moreParams.start = '';
        vm.moreParams.end = '';
        vm.moreParams.timestamp = Date.now();
        await vm.setBets();
        return;
      }

      // If date range is incomplete, return without doing anything
      if (!this.date[0] || !this.date[1]) return;

      // Update date filter values
      vm.moreParams.start = vm.formatDate(this.date[0]);
      vm.moreParams.end = vm.formatDate(this.date[1]);
      vm.moreParams.timestamp = Date.now();

      // Log that date filter was updated and fetch data
      console.log('Date filter updated, fetching data with new date range...');
      await vm.setBets();
    },

    // Format currency with commas and put negative figures in brackets (standardized from master)
    formatCurrency(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0.00';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    },

    // Format numbers with commas (2 decimals for amounts) and put negative figures in brackets
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0.00';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    },

    // Format number with commas for totals (standardized from master)
    formatNumberWithCommas(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0.00';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    },

    formatDate(date) {
      var d = new Date(date),
        month = '' + (d.getMonth() + 1),
        day = '' + d.getDate(),
        year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    // Pagination and dropdowns
    goToPage(page) {
      this.offset = page;
      this.moreParams.page = page;
      this.moreParams.limit = this.limit;
      this.setBets();
    },


    // Method to handle limit change
    changeLimit(newLimit) {
      this.limit = newLimit;
      this.moreParams.limit = newLimit;
      this.offset = 1; // Reset to first page when changing limit
      this.moreParams.page = 1;
      this.setBets();
    },

    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    toggleDetails(data) {
      this.viewModelOpen = true;
      this.deposit = data;
    },

    applyFilters(filters) {
      this.moreParams.page = ''
      this.moreParams.offset = ''
      this.setBets()
    },

    resetFilters() {
      // Reset all filter parameters
      this.moreParams = {
        stake_amount_min: '',
        stake_amount_max: '',
        winning_amount_min: '',
        winning_amount_max: '',
        odds_min: '',
        odds_max: '',
        selections_min: '',
        selections_max: '',
        bet_type: '',
        selection_type: '',
        profile_id: '',
        ip: '',
        mobile_number: '',
        status: '',
        unsettled: '',
        sort: '',
        start: '',
        end: '',
        page: '',
        limit: "100",
        timestamp: 'timestamp',
        skip_cache: '',
      };

      // Reset dropdown selections
      this.selected_bet_status = { text: 'All', value: '' };
      this.selected_selection_type = { text: 'All', value: '' };
      this.selected_placement_type = { text: 'All', value: '' };

      // Reset date picker
      this.date = null;

      // Fetch data with reset filters
      this.setBets();
    },

    async setBets() {
      let app = this
      app.isLoading = true

      // Ensure limit is set in params
      app.moreParams.limit = app.limit.toString();

      let response = await this.getSportsBets(app.moreParams)

      app.bets = []
      app.total = 0
      if (response.status === 200) {
        app.bets = response.message.result

        if (response.message.record_count !== 0) {
          // Convert string to number using parseInt
          app.total = parseInt(response.message.record_count)
        }

        app.showDropdown = []
        for (let i = 0; i < app.bets.length; i++) {
          app.showDropdown.push(false)
        }
      } else {
        app.bets = [];
        app.total = 0;
      }
      app.isLoading = false
    },

    getExtraDataValue(data, key) {
      if (!data.extra_data) return null; // Check if extra_data exists

      try {
        const extraData = JSON.parse(data.extra_data); // Parse JSON string
        console.log("extraData", extraData);
        return extraData[key] ?? null; // Return the requested key or null if not found
      } catch (error) {
        console.error("Error parsing extra_data:", error);
        return null;
      }
    },

    statusClass(status) {
      // Apply color based on the status
      if (status === "0") return "bg-red-500";
      if (status === "1") return "bg-green-500";
      return "bg-yellow-500"; // For other statuses
    },
    statusText(status) {
      if (status === "0") return "Not Started";
      if (status === "1") return "In Progress";
      return "Finished"; // For other statuses
    },
    betTypeText(status) {
      if (status === "0") return "Cash Bet";
      if (status === "1") return "Bonus Bet";
      if (status === "2") return "Free Bet";
    },
    processedText(status) {
      if (status === "0") return "PENDING";
      if (status === "1") return "WON";
      if (status === "3") return "LOST";
      if (status === "7") return "VOIDED";
      if (status === "9") return "CANCELED";
    },
    // Method to parse extra_data and return as an object
    parseExtraData(extraData) {
      try {
        return JSON.parse(extraData);  // Parse the extra_data JSON string into an object
      } catch (error) {
        console.error('Error parsing extra_data:', error);
        return {};  // Return an empty object if parsing fails
      }
    },
    // Method to extract and return specific values from parsed extra_data
    getExtraDataValues(item) {
      try {
        if (!item.extra_data) return {};
        return typeof item.extra_data === 'string'
          ? JSON.parse(item.extra_data)
          : item.extra_data;
      } catch (e) {
        console.error("Error parsing extra_data:", e);
        return {};
      }
    },
    getBetTypeClass(betType) {
      const typeMap = {
        '0': 'bet-type-cash',
        '1': 'bet-type-bonus',
        '2': 'bet-type-free'
      };

      return typeMap[betType] || 'bet-type-default';
    },

    getBetTypeText(betType) {
      const typeMap = {
        '0': 'Cash Bet',
        '1': 'Bonus Bet',
        '2': 'Free Bet'
      };

      return typeMap[betType] || 'Unknown';
    },

    getStatusClass(status, item) {
      // Check if this is a WON bet but credit transaction is null
      if (status === '1' && item && item.bet_credit_transaction_id === null) {
        return 'status-won-pending'; // Orange background for WON bets without credit
      }

      const statusMap = {
        '0': 'status-pending',
        '1': 'status-won',
        '2': 'status-pending',
        '3': 'status-lost'
      };

      return statusMap[status] || 'status-default';
    },

    getStatusText(status, item) {
      // Check if this is a WON bet but credit transaction is null
      if (status === '1' && item && item.bet_credit_transaction_id === null) {
        return 'WON';
      }

      const statusMap = {
        '0': 'Pending',
        '1': 'Won',
        '3': 'Lost',
        '7': 'Voided',
        '9': 'Canceled'
      };
      return statusMap[status] || 'Unknown';
    },


    // Update the view BetSlip method to use the carousel transition
    async viewBetSlip(item) {
      let app = this;

      // Prevent duplicate calls
      if (app.isProcessingBetSlip) return;
      app.isProcessingBetSlip = true;

      await app.fillBetDetails(item)
      // Navigate to the BetSlipDetails page with the bet ID
      this.$router.push({
        name: 'sport-bet-slip',
      });

      app.isProcessingBetSlip = false;
    },

    // Resettle Bet
    resettleBet(item) {
      let app = this;

      const payload = {
        "bet_id": item.bet_id,
        "timestamp": Date.now()
      }

      console.log("resettleBet payload: ", JSON.stringify(payload))

      app.$swal.fire({
        title: 'Resettle Bet',
        text: "Are you sure you want to resettle this bet? This will process the payout for this winning bet.",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, resettle!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          // Use the /resettle/{betId} endpoint
          return await this.resettleBetSlip(payload);
        },
      }).then(async result => {
        if (result.value && result.value.status === 200) {
          this.$swal.fire({
            title: 'Success!',
            text: result.value.message || 'Bet has been resettled successfully.',
            icon: 'success',
            confirmButtonText: 'OK'
          }).then(() => {
            // Refresh the bets list to show updated status
            this.setBets();
          });
        } else {
          this.$swal.fire({
            title: 'Error!',
            text: result.value?.message || 'Failed to resettle bet.',
            icon: 'error',
            confirmButtonText: 'OK'
          });
        }
      })
    },

    // cancel Bet
    cancelBet(item) {
      let app = this;

      const payload = {
        "bet_id": item.bet_id,
        "timestamp": Date.now()
      }

      console.log("cancelCustomerBet payload: ", JSON.stringify(payload))

      app.$swal.fire({
        title: 'Cancel Bet',
        text: "Are you sure you want to cancel this bet?",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, Cancel!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          // Use the /resettle/{betId} endpoint
          return await this.cancelCustomerBet(payload);
        },
      }).then(async result => {
        if (result.value && result.value.status === 200) {
          this.$swal.fire({
            title: 'Success!',
            text: result.value.message || 'Bet has been resettled successfully.',
            icon: 'success',
            confirmButtonText: 'OK'
          }).then(() => {
            // Refresh the bets list to show updated status
            this.
            this.setBets();
          });
        } else {
          this.$swal.fire({
            title: 'Error!',
            text: result.value?.message || 'Failed to resettle bet.',
            icon: 'error',
            confirmButtonText: 'OK'
          });
        }
      })
    },


    // KRA Report methods
    getKraReportText(kraReport) {
      const reportMap = {
        '0': 'Not Reported',
        '1': 'Reported'
      };
      return reportMap[kraReport] || 'Unknown';
    },

    getKraReportClass(kraReport) {
      const reportMap = {
        '0': 'status-pending', // Not Reported - gray
        '1': 'status-won'      // Reported - green
      };
      return reportMap[kraReport] || 'status-default';
    },

    // Risk State methods
    getRiskStateText(riskState) {
      const stateMap = {
        '3': 'Not Submitted',
        '1': 'Submitted'
      };
      return stateMap[riskState] || 'Unknown';
    },

    getRiskStateClass(riskState) {
      const stateMap = {
        '3': 'status-pending', // Not Submitted - gray
        '1': 'status-won'      // Submitted - green
      };
      return stateMap[riskState] || 'status-default';
    },

    // Update the existing status methods to match the requirements
    getStatusText(status, item) {
      // Check if this is a WON bet but credit transaction is null
      if (status === '1' && item && item.bet_credit_transaction_id === null) {
        return 'Won (Pending Credit)';
      }

      const statusMap = {
        '0': 'Pending',
        '1': 'Won',
        '3': 'Lost',
        '7': 'Voided',
        '9': 'Canceled'
      };
      return statusMap[status] || 'Unknown';
    },

    getStatusClass(status, item) {
      // Check if this is a WON bet but credit transaction is null
      if (status === '1' && item && item.bet_credit_transaction_id === null) {
        return 'status-won-pending'; // Special class for won but pending credit
      }

      const statusMap = {
        '0': 'status-pending', // Pending - gray
        '1': 'status-won',     // Won - green
        '3': 'status-lost'     // Lost - orange
      };
      return statusMap[status] || 'status-default';
    },
    isMatchFinished(item) {
      if (!item.start_time) return false;

      const startTime = new Date(item.start_time);
      const currentTime = new Date();

      // Calculate the difference in milliseconds
      const timeDifference = currentTime - startTime;

      // Convert to hours (2.5 hours = 9000000 milliseconds)
      const hoursDifference = timeDifference / (1000 * 60 * 60);

      // Return true if more than 2.5 hours have passed since the start time
      return hoursDifference > 2.5;
    },

    getStatusDisplay(item) {
      // If the match is finished based on time but not marked as won/lost
      if (this.isMatchFinished(item) && item.bet_status !== '1' && item.bet_status !== '3') {
        return "Finished";
      }

      // Otherwise use the regular status text
      return this.getStatusText(item.bet_status, item);
    },

    goToModalPage(page) {
      this.modal_offset = page;
      this.bet_slip_params.page = page;
      this.fetchBetSlips();
    },

    changeModalLimit(limit) {
      this.modal_limit = limit;
      this.bet_slip_params.limit = limit;
      this.modal_offset = 1;
      this.bet_slip_params.page = 1;
      this.fetchBetSlips();
    },

    async fetchBetSlips() {
      let app = this;
      app.isLoading = true;

      // Update timestamp
      app.bet_slip_params.timestamp = Date.now();

      // Call the API
      let response = await this.getSportsBetSlips(app.bet_slip_params);

      if (response.status === 200) {
        app.bet_slips = response.message.result;
        app.modal_total = parseInt(response.message.record_count || 0);
      } else {
        app.bet_slips = [];
        app.modal_total = 0;
      }

      app.isLoading = false;
    },

    // Modal methods
    openRiskKraModal(item) {
      this.selectedBetItem = item;
      this.isRiskKraModalOpen = true;
    },

    closeRiskKraModal() {
      this.isRiskKraModalOpen = false;
      this.selectedBetItem = null;
    },

    openExtraDataModal(item) {
      this.selectedBetItem = item;
      this.isExtraDataModalOpen = true;
    },

    closeExtraDataModal() {
      this.isExtraDataModalOpen = false;
      this.selectedBetItem = null;
    },

    formatExtraData(extraData) {
      if (!extraData) return 'No extra data available';

      try {
        // If it's already an object, stringify it with formatting
        if (typeof extraData === 'object') {
          return JSON.stringify(extraData, null, 2);
        }

        // If it's a string, try to parse and reformat it
        const parsed = JSON.parse(extraData);
        return JSON.stringify(parsed, null, 2);
      } catch (error) {
        // If parsing fails, return the raw data
        return extraData;
      }
    },



    // Simple method to extract values from extra_data using keys
    getExtraDataValue(data, key) {
      if (!data || !data.extra_data) return null;

      try {
        let extraData;

        // Handle both string and object formats
        if (typeof data.extra_data === 'string') {
          extraData = JSON.parse(data.extra_data);
        } else {
          extraData = data.extra_data;
        }

        // Support nested key access using dot notation (e.g., "bet_data.selected", "balance.amount")
        const keys = key.split('.');
        let value = extraData;

        for (const k of keys) {
          if (value && typeof value === 'object' && k in value) {
            value = value[k];
          } else {
            return null;
          }
        }

        return value;
      } catch (error) {
        console.error("Error parsing extra_data:", error);
        return null;
      }
    },

    // Method to extract extra data and return as key-value pairs
    getExtraDataKeyValuePairs(extraData) {
      if (!extraData) return [];

      try {
        let parsedData;

        // Handle both string and object formats
        if (typeof extraData === 'string') {
          parsedData = JSON.parse(extraData);
        } else {
          parsedData = extraData;
        }

        const keyValuePairs = [];

        // Recursive function to flatten nested objects
        const flattenObject = (obj, prefix = '') => {
          for (const [key, value] of Object.entries(obj)) {
            const fullKey = prefix ? `${prefix}.${key}` : key;

            if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
              // If it's a nested object, recurse
              flattenObject(value, fullKey);
            } else {
              // Add the key-value pair
              keyValuePairs.push({
                key: fullKey,
                value: Array.isArray(value) ? JSON.stringify(value) : String(value)
              });
            }
          }
        };

        flattenObject(parsedData);
        return keyValuePairs;

      } catch (error) {
        console.error("Error parsing extra_data:", error);
        return [];
      }
    }
  },
}
</script>

<style scoped>
/* Add these styles for bet type and status badges */
.bet-type-badge {
  display: inline-block;
  padding: 6px 12px;
  min-width: 90px;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  letter-spacing: 0.3px;
}

.bet-type-cash {
  background-color: #3498DB;
  /* Blue */
}

.bet-type-bonus {
  background-color: #9b59b6;
  /* Purple */
}

.bet-type-free {
  background-color: #2ecc71;
  /* Green */
}

.bet-type-default {
  background-color: #95a5a6;
  /* Gray */
}

/* Status badges with updated colors */
.status-badge {
  display: inline-block;
  padding: 6px 12px;
  min-width: 90px;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  letter-spacing: 0.3px;
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  filter: brightness(105%);
}

.status-won {
  background-color: #2ecc71;
  /* Green */
}

.status-won-pending {
  background-color: #f39c12;
  /* Orange */
  border: 2px solid #2ecc71;
  /* Green border */
}

.status-lost {
  background-color: #e74c3c;
  /* Red */
}

.status-pending {
  background-color: #f39c12;
  /* Orange */
}

.status-default {
  background-color: #95a5a6;
  /* Gray */
}
</style>
