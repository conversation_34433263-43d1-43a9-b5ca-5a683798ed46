<template>
  <div class="p-6">

    <PageHeader
      title="Scheduled SMS"
      subtitle="View and manage scheduled SMS messages"
      :show-back-button="true"
      @back="goBack"
    />

    <!-- Quick Navigation -->
    <div class="mx-6 mb-4 flex justify-end">
      <router-link
        to="/app/bulk-sms"
        class="px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors flex items-center space-x-2"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        <span>Schedule New SMS</span>
      </router-link>
    </div>

    <CustomLoading v-if="isLoading" />

    <!-- Summary Cards -->
    <div v-if="!isLoading && data.length > 0" class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-center">
          <div class="p-2 bg-blue-100 rounded-lg">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-blue-600">Total Scheduled</p>
            <p class="text-2xl font-bold text-blue-900">{{ total.toLocaleString() }}</p>
          </div>
        </div>
      </div>

      <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 rounded-lg">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-green-600">Pending</p>
            <p class="text-2xl font-bold text-green-900">{{ pendingCount.toLocaleString() }}</p>
          </div>
        </div>
      </div>

      <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <div class="flex items-center">
          <div class="p-2 bg-orange-100 rounded-lg">
            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-orange-600">Ready to Send</p>
            <p class="text-2xl font-bold text-orange-900">{{ readyCount.toLocaleString() }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Data Table -->
    <auto-table
      :data="data"
      :loading="isLoading"
      :headers="tableHeaders"
      :has-actions="true"
      :total-items="total"
      :items-per-page="limit"
      :current-page-prop="offset"
      :server-side-pagination="true"
      :pagination="total > limit"
      :show-items-count="true"
      :exclude-columns=excludeColumns
      :table-margin-bottom="12"
      @page-change="gotToPage"
    >
      <!-- Index Column -->
      <template #index="{ index }">
        <div class="text-center">{{ index + 1 + ((offset - 1) * limit) }}</div>
      </template>

      <!-- Campaign ID Column -->
      <template #campaign_id="{ item }">
        <span class="font-medium">#{{ item.campaign_id || item.id }}</span>
      </template>

      <!-- Message Column -->
      <template #message="{ item }">
        <div class="max-w-xs">
          <span class="text-sm">{{ truncateMessage(item.message) }}</span>
          <button 
            v-if="item.message && item.message.length > 50"
            @click="showFullMessage(item)"
            class="text-blue-600 hover:text-blue-800 text-xs ml-1"
          >
            View Full
          </button>
        </div>
      </template>

      <!-- Recipients Column -->
      <template #recipients="{ item }">
        <span class="font-medium">{{ (item.recipients || item.recipient_count || 0).toLocaleString() }}</span>
      </template>

      <!-- SMS Pages Column -->
      <template #sms_pages="{ item }">
        <span class="text-sm">{{ item.sms_pages || 1 }}</span>
      </template>

      <!-- Is Scheduled Column -->
      <template #is_scheduled="{ item }">
        <span class="px-2 py-1 text-xs font-medium rounded-full" :class="getScheduledStatusClass(item.is_scheduled)">
          {{ item.is_scheduled == 1 ? 'Yes' : 'No' }}
        </span>
      </template>

      <!-- Schedule Date Column -->
      <template #schedule_date="{ item }">
        <span class="text-sm">{{ formatDate(item.schedule_date || item.scheduled_datetime) }}</span>
      </template>

      <!-- Schedule Time Column -->
      <template #schedule_time="{ item }">
        <span class="text-sm">{{ formatTime(item.schedule_time || item.scheduled_datetime) }}</span>
      </template>

      <!-- Status Column -->
      <template #status="{ item }">
        <span 
          class="px-2 py-1 text-xs font-medium rounded-full"
          :class="getStatusClass(item.status)"
        >
          {{ getStatusText(item.status) }}
        </span>
      </template>

      <!-- Updated At Column -->
      <template #updated_at="{ item }">
        <span class="text-xs text-gray-500">{{ moment(item.updated_at).format('MMM DD, YYYY HH:mm') }}</span>
      </template>

      <!-- Actions Column -->
      <template #actions="{ item, index }">
        <action-dropdown button-text="Actions" :show-text="true" :show-icon="true">

          <action-item text="View Details" color="blue" @click="viewDetails(item)">
            <template #icon>
              <svg class="mr-3 h-5 w-5 text-blue-500 group-hover:text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </template>
          </action-item>

          <action-item 
            text="Edit Message" 
            color="green" 
            @click="editMessage(item)"
          >
            <template #icon>
              <svg class="mr-3 h-5 w-5 text-green-500 group-hover:text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </template>
          </action-item>

          <action-item 
            text="Reschedule" 
            color="yellow" 
            @click="rescheduleMessage(item)"
          >
            <template #icon>
              <svg class="mr-3 h-5 w-5 text-yellow-500 group-hover:text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </template>
          </action-item>

          <action-item 
            text="Unschedule" 
            color="orange" 
            @click="unscheduleMessage(item)"
          >
            <template #icon>
              <svg class="mr-3 h-5 w-5 text-orange-500 group-hover:text-orange-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
              </svg>
            </template>
          </action-item>

          <action-item 
            text="Cancel" 
            color="red" 
            @click="cancelScheduledMessage(item)"
          >
            <template #icon>
              <svg class="mr-3 h-5 w-5 text-red-500 group-hover:text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </template>
          </action-item>

        </action-dropdown>
      </template>
    </auto-table>

    <!-- No Data Message -->
    <div v-if="!isLoading && data.length === 0" class="text-center py-8">
      <div class="text-gray-400 text-lg mb-2">📅</div>
      <p class="text-gray-500">No scheduled SMS messages found</p>
      <router-link 
        to="/app/bulk-sms" 
        class="text-blue-600 hover:text-blue-800 text-sm mt-2 inline-block"
      >
        Schedule a new message
      </router-link>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import { mapActions } from "vuex";
import { AutoTable, CustomLoading, ActionDropdown, ActionItem } from "@/components/common";
import PageHeader from "@/components/common/PageHeader.vue";

export default {
  components: {
    AutoTable,
    CustomLoading,
    PageHeader,
    ActionDropdown,
    ActionItem,
  },
  data() {
    return {
      isLoading: false,
      data: [],
      total: 0,
      limit: 100,
      offset: 1,

      moreParams: {
        page: 1,
        limit: 100,
        timestamp: 'timestamp',
      },
      tableHeaders:[
        { key:"campaign_id", label:"Campaign Id", align: 'left'},
        { key: 'message', label: 'Message', align: 'left' },
        { key: 'recipients', label: 'Recipients', align: 'left' },
        { key: 'sms_pages', label: 'SMS Pages', align: 'left' },
        { key: 'is_scheduled', label: 'Scheduled?', align: 'left' },
        { key: 'schedule_date', label: 'Schedule Date', align: 'left' },
        { key: 'schedule_time', label: 'Schedule Time', align: 'left' },
        { key: 'status', label: 'Status', align: 'left' },
        { key: 'updated_at', label: 'Updated At', align: 'left' },
      ],
      excludeColumns: [
        'user_id',
        'bulk_filter',
        'trx_count',
        'created_at',
        'completed_on',
        'short_code',
        'id'
      ],
    }
  },
  computed: {
    pendingCount() {
      return this.data.filter(item => item.status === 200 || item.status === 0).length;
    },
    readyCount() {
      return this.data.filter(item => item.status === 1).length;
    }
  },
  mounted() {
    this.loadScheduledSMS();
  },
  methods: {
    ...mapActions(["getScheduledSMS", "executeScheduledSMS", "updateScheduledSMS", "toggleSideMenu"]),
    
    moment,

    goBack() {
      this.$router.go(-1);
    },

    async loadScheduledSMS() {
      this.isLoading = true;

      try {
        const response = await this.getScheduledSMS(this.moreParams);
        
        if (response.status === 200) {
          this.data = response.message.result || [];
          this.total = parseInt(response.message.record_count) || this.data.length;
        } else {
          this.data = [];
          this.total = 0;
          this.$swal.fire('Error!', response.message || 'Failed to load scheduled SMS', 'error');
        }
      } catch (error) {
        console.error('Error loading scheduled SMS:', error);
        this.data = [];
        this.total = 0;
        this.$swal.fire('Error!', 'Failed to load scheduled SMS', 'error');
      }

      this.isLoading = false;
    },

    gotToPage(page) {
      this.moreParams.page = page;
      this.offset = page;
      this.loadScheduledSMS();
    },

    formatDate(datetime) {
      if (!datetime) return 'N/A';
      return moment(datetime).format('MMM DD, YYYY');
    },

    formatTime(datetime) {
      if (!datetime) return 'N/A';
      return moment(datetime).format('HH:mm');
    },

    truncateMessage(message) {
      if (!message) return 'N/A';
      return message.length > 50 ? message.substring(0, 50) + '...' : message;
    },

    getStatusClass(status) {
      switch (status) {
        case 'pending':
        case 0:
        case 200:
          return 'bg-yellow-100 text-yellow-800';
        case 'sent':
        case 1:
        case 700:
          return 'bg-green-100 text-green-800';
        case 'failed':
        case 2:
        case 701:
          return 'bg-red-100 text-red-800';
        case 'cancelled':
        case 3:
        case 505:
          return 'bg-gray-100 text-gray-800';
        case 506:
          return 'bg-blue-100 text-blue-800';
        default:
          return 'bg-blue-100 text-blue-800';
      }
    },

    getScheduledStatusClass(isScheduled) {
      return isScheduled == 1 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
    },

    getStatusText(status) {
      switch (status) {
        case 'pending':
        case 0:
        case 200:
          return 'Pending';
        case 'sent':
        case 1:
        case 700:
          return 'Sent';
        case 'failed':
        case 2:
        case 701:
          return 'Failed';
        case 'cancelled':
        case 3:
        case 505:
          return 'Cancelled';
        case 506:
          return 'Unscheduled';
        default:
          return 'Unknown';
      }
    },

    canEdit(item) {
      return (item.status === 'pending' || item.status === 0 || item.status === 200) && item.is_scheduled == 1;
    },

    canCancel(item) {
      return item.status === 'pending' || item.status === 0 || item.status === 200; 
    },

    canReschedule(item) {
      return (item.status === 'pending' || item.status === 0 || item.status === 200) && item.is_scheduled == 1;
    },

    canUnschedule(item) {
      return (item.status === 'pending' || item.status === 0 || item.status === 200) && item.is_scheduled == 1;
    },

    async editMessage(item) {
      const { value: newMessage } = await this.$swal.fire({
        title: 'Edit Message',
        input: 'textarea',
        inputLabel: 'Message Content',
        inputValue: item.message,
        inputAttributes: {
          'aria-label': 'Type your message here',
          style: 'height: 120px;'
        },
        showCancelButton: true,
        confirmButtonText: 'Update Message',
        cancelButtonText: 'Cancel',
        inputValidator: (value) => {
          if (!value || value.trim().length === 0) {
            return 'Please enter a message!'
          }
          if (value.length > 1000) {
            return 'Message is too long (max 1000 characters)'
          }
        }
      });

      if (newMessage && newMessage.trim() !== item.message) {
        try {
          this.isLoading = true;
          
          const updateData = {
            id: item.id,
            id:item.campaign_id,
            approval: "200",
            message: newMessage.trim(),
            shortCode: item.short_code || "",
            isScheduled: item.is_scheduled.toString(),
            scheduleDate: item.schedule_date || "",
            scheduleTime: item.schedule_time || ""
          };

          const response = await this.updateScheduledSMS(updateData);
          
          if (response.status === 200) {
            this.$swal.fire('Success!', 'Message updated successfully', 'success');
            this.loadScheduledSMS(); // Refresh the list
          } else {
            this.$swal.fire('Error!', response.message || 'Failed to update message', 'error');
          }
        } catch (error) {
          console.error('Error updating message:', error);
          this.$swal.fire('Error!', 'Failed to update message', 'error');
        } finally {
          this.isLoading = false;
        }
      }
    },

    async rescheduleMessage(item) {
      const { value: formValues } = await this.$swal.fire({
        title: 'Reschedule Message',
        html: `
          <div class="text-left">
            <label class="block text-sm font-medium text-gray-700 mb-1">Schedule Date</label>
            <input id="schedule-date" type="date" class="w-full p-2 border border-gray-300 rounded-md mb-3" value="${moment(item.schedule_date).format('YYYY-MM-DD')}" min="${moment().format('YYYY-MM-DD')}">
            
            <label class="block text-sm font-medium text-gray-700 mb-1">Schedule Time</label>
            <input id="schedule-time" type="time" class="w-full p-2 border border-gray-300 rounded-md" value="${moment(item.schedule_time, 'HH:mm:ss').format('HH:mm')}">
          </div>
        `,
        focusConfirm: false,
        showCancelButton: true,
        confirmButtonText: 'Reschedule',
        cancelButtonText: 'Cancel',
        preConfirm: () => {
          const date = document.getElementById('schedule-date').value;
          const time = document.getElementById('schedule-time').value;
          
          if (!date || !time) {
            this.$swal.showValidationMessage('Please select both date and time');
            return false;
          }
          
          const scheduledDateTime = moment(`${date} ${time}`);
          if (scheduledDateTime.isBefore(moment())) {
            this.$swal.showValidationMessage('Please select a future date and time');
            return false;
          }
          
          return { date, time };
        }
      });

      if (formValues) {
        try {
          this.isLoading = true;
          
          const updateData = {
            id: item.id,
            id:item.campaign_id,
            approval: "200",
            message: item.message,
            shortCode: item.short_code || "",
            isScheduled: "1",
            scheduleDate: formValues.date,
            scheduleTime: formValues.time
          };

          const response = await this.updateScheduledSMS(updateData);
          
          if (response.status === 200) {
            this.$swal.fire('Success!', 'Message rescheduled successfully', 'success');
            this.loadScheduledSMS();
          } else {
            this.$swal.fire('Error!', response.message || 'Failed to reschedule message', 'error');
          }
        } catch (error) {
          console.error('Error rescheduling message:', error);
          this.$swal.fire('Error!', 'Failed to reschedule message', 'error');
        } finally {
          this.isLoading = false;
        }
      }
    },

    async unscheduleMessage(item) {
      const result = await this.$swal.fire({
        title: 'Unschedule Message?',
        text: 'This will remove the schedule and the message will be sent immediately.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#f59e0b',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Yes, unschedule it!',
        cancelButtonText: 'Cancel'
      });

      if (result.isConfirmed) {
        try {
          this.isLoading = true;
          
          const updateData = {
            id: item.id,
            id:item.campaign_id,
            approval: "506", // Unschedule status
            message: item.message,
            shortCode: item.short_code || "",
            isScheduled: "0",
            scheduleDate: "",
            scheduleTime: ""
          };

          const response = await this.updateScheduledSMS(updateData);
          
          if (response.status === 200) {
            this.$swal.fire('Success!', 'Message unscheduled successfully', 'success');
            this.loadScheduledSMS();
          } else {
            this.$swal.fire('Error!', response.message || 'Failed to unschedule message', 'error');
          }
        } catch (error) {
          console.error('Error unscheduling message:', error);
          this.$swal.fire('Error!', 'Failed to unschedule message', 'error');
        } finally {
          this.isLoading = false;
        }
      }
    },

    async cancelScheduledMessage(item) {
      const result = await this.$swal.fire({
        title: 'Cancel Scheduled Message?',
        text: `This will permanently cancel the scheduled message for ${(item.recipients || item.recipient_count || 0).toLocaleString()} recipients. This action cannot be undone.`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Yes, cancel it!',
        cancelButtonText: 'Keep Message',
        reverseButtons: true
      });

      if (result.isConfirmed) {
        try {
          this.isLoading = true;
          
          const updateData = {
            id: item.id,
            id:item.campaign_id,
            approval: "505", // Cancel status
            message: item.message,
            shortCode: item.short_code || "",
            isScheduled: item.is_scheduled.toString(),
            scheduleDate: item.schedule_date || "",
            scheduleTime: item.schedule_time || ""
          };

          const response = await this.updateScheduledSMS(updateData);
          
          if (response.status === 200) {
            this.$swal.fire('Cancelled!', 'The scheduled message has been cancelled.', 'success');
            this.loadScheduledSMS(); // Refresh the list
          } else {
            this.$swal.fire('Error!', response.message || 'Failed to cancel scheduled message', 'error');
          }
        } catch (error) {
          console.error('Error cancelling message:', error);
          this.$swal.fire('Error!', 'Failed to cancel scheduled message', 'error');
        } finally {
          this.isLoading = false;
        }
      }
    },

    showFullMessage(item) {
      this.$swal.fire({
        title: 'Full Message',
        text: item.message,
        icon: 'info',
        confirmButtonText: 'Close',
        width: '600px'
      });
    },

    viewDetails(item) {
      const details = `
        <div class="text-left space-y-2">
          <p><strong>Campaign ID:</strong> ${item.campaign_id || item.id || 'N/A'}</p>
          <p><strong>Recipients:</strong> ${(item.recipients || item.recipient_count || 0).toLocaleString()}</p>
          <p><strong>SMS Pages:</strong> ${item.sms_pages || 1}</p>
          <p><strong>Scheduled:</strong> ${item.is_scheduled == 1 ? 'Yes' : 'No'}</p>
          <p><strong>Schedule Date:</strong> ${this.formatDate(item.schedule_date || item.scheduled_datetime)}</p>
          <p><strong>Schedule Time:</strong> ${this.formatTime(item.schedule_time || item.scheduled_datetime)}</p>
          <p><strong>Status:</strong> <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getStatusClass(item.status).replace('bg-', 'bg-opacity-20 bg-').replace('text-', 'text-')}">${this.getStatusText(item.status)}</span></p>
          <p><strong>Updated:</strong> ${moment(item.updated_at).format('YYYY-MM-DD HH:mm:ss')}</p>
          <div class="mt-3">
            <p><strong>Message:</strong></p>
            <div class="mt-2 p-3 bg-gray-50 rounded-lg text-sm border">${item.message || 'N/A'}</div>
          </div>
        </div>
      `;

      this.$swal.fire({
        title: 'SMS Details',
        html: details,
        icon: 'info',
        confirmButtonText: 'Close',
        width: '700px'
      });
    }
  }
}
</script>

<style scoped>
/* Ensure proper spacing and alignment */
.space-y-2 > * + * {
  margin-top: 0.5rem;
}
</style>