<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Greener" pageSubtitle="Monday" />

    <!-- Search Filters Panel -->
    <div class="bg-white rounded-lg shadow-lg mx-3 mb-4">
      <div class="p-4 border-b">
        <h3 class="text-lg font-medium text-gray-700">Filters</h3>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
        <!-- Phone Number Filter -->
        <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-1">Customer Identification</h4>
          <div>
            <label class="block text-xs font-bold text-gray-700 mb-1">Mobile Number</label>
            <input 
              type="text" 
              v-model="payload.phone_number" 
              placeholder="Enter mobile number"
              class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
              @keyup.enter="applyFilters()">
          </div>
        </div>

        <!-- Date Range Filter -->
        <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-1">Date Range</h4>
          <div>
            <label class="block text-xs text-gray-600 mb-1">Select Date Range</label>
            <VueDatePicker 
              v-model="date" 
              range 
              multi-calendars 
              :enable-time-picker="false" 
              :format="'yyyy-MM-dd'"
              :preset-ranges="presetRanges" 
              placeholder="Select date range" 
              class="w-full text-xs"
              @update:model-value="selectDate" />
          </div>
        </div>

        <!-- Limit Filter -->
        <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-1">Records Per Page</h4>
          <div>
            <label class="block text-xs text-gray-600 mb-1">Limit</label>
            <select
              class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
              v-model="payload.limit" 
              @change="applyFilters()">
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="p-4 border-t flex justify-between items-center">
        <button 
          @click="resetFilters"
          class="px-4 py-2 bg-gray-500 text-white text-sm rounded-md hover:bg-gray-600 transition-colors">
          Reset Filters
        </button>
        
        <button 
          @click="exportToExcel"
          :disabled="!data.length || isLoading"
          class="px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors flex items-center disabled:bg-gray-400 disabled:cursor-not-allowed">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
            </path>
          </svg>
          Export to Excel
        </button>
      </div>
    </div>

    <!-- Data Table -->
    <div class="block p-3 bg-white overflow-x-auto">
      <auto-table 
        :data="data" 
        :has-actions="false"
        :loading="isLoading" 
        :total-items="total" 
        :decimal-places="decimalPlaces"
        :items-per-page="limit" 
        :current-page-prop="offset" 
        :server-side-pagination="true" 
        :pagination="total > limit" 
        :exclude-columns="excludeColumn"
        :show-items-count="true" 
        @page-change="gotToPage">
        
        <!-- Custom column templates -->
        <template #Total_Win_amount="{ item }">
          <div class="font-bold text-green-600">{{ formatCurrency(item.Total_Win_amount) }}</div>
        </template>

        <template #Total_Deposits="{ item }">
          <div class="font-bold text-blue-600">{{ formatCurrency(item.Total_Deposits) }}</div>
        </template>

        <template #Total_Withdrawals="{ item }">
          <div class="font-bold text-orange-600">{{ formatCurrency(item.Total_Withdrawals) }}</div>
        </template>

        <template #Total_Stake="{ item }">
          <div class="font-bold">{{ formatCurrency(item.Total_Stake) }}</div>
        </template>

        <template #Total_odds="{ item }">
          <div class="font-bold">{{ formatCurrency(item.Total_odds) }}</div>
        </template>

        <template #NGR="{ item }">
          <div class="font-bold" :class="item.NGR >= 0 ? 'text-green-600' : 'text-red-600'">
            {{ formatCurrency(item.NGR) }}
          </div>
        </template>
      </auto-table>
    </div>
  </div>
</template>

<script>
import 'vue-loading-overlay/dist/vue-loading.css';
import moment from "moment";
import { mapActions } from "vuex";
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'
import { AutoTable, CustomLoading } from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';
import { startOfMonth, endOfMonth, subMonths, startOfYear, endOfYear } from 'date-fns';
import * as XLSX from 'xlsx';

export default {
  components: {
    AutoTable,
    CustomLoading,
    PageHeader,
    VueDatePicker,
  },
  
  data() {
    return {
      isLoading: false,
      total: 0,
      limit: 100,
      offset: 1,
      payload: {
        phone_number: "",
        start: "",
        end: "",
        page: 1,
        limit: 100,
        timestamp: "",
        sort: "",
      },
      data: [],
      date: null,
      presetRanges: [
        { label: 'Today', range: [new Date(), new Date()] },
        { label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())] },
        { label: 'Last month', range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))] },
        { label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())] },
      ],
      decimalPlaces: {
        Total_odds: 2,
        Total_Stake: 2,
        Total_Win_amount: 2,
        Total_Deposits: 2,
        Total_Withdrawals: 2,
        NGR: 2,
      },
      excludeColumn: [
        "bet_date",
        "First_Bet_Date",
        "Last_Bet_Date",
      ],
    }
  },

  async mounted() {
    await this.setGreenerMonday()
  },

  methods: {
    ...mapActions(["getGreenerMonday"]),

    async setGreenerMonday() {
      this.isLoading = true
      this.payload.timestamp = Date.now()

      try {
        let response = await this.getGreenerMonday(this.payload)
        if (response.status === 200) {
          this.data = response.message.result || []
          this.total = parseInt(response.message.record_count) || 0
        } else {
          this.data = []
          this.total = 0
        }
      } catch (error) {
        console.error('Error fetching data:', error)
        this.data = []
        this.total = 0
      }

      this.isLoading = false
    },

    async applyFilters() {
      this.offset = 1
      this.payload.page = 1
      await this.setGreenerMonday()
    },

    resetFilters() {
      this.payload = {
        phone_number: "",
        start: "",
        end: "",
        page: 1,
        limit: 100,
        timestamp: "",
        sort: "",
      }
      this.date = null
      this.offset = 1
      this.limit = 100
      this.setGreenerMonday()
    },

    gotToPage(page) {
      this.payload.page = page
      this.offset = page
      this.setGreenerMonday()
    },

    async selectDate() {
      if (this.date && Array.isArray(this.date) && this.date.length >= 2) {
        this.payload.start = this.formatDate(this.date[0])
        this.payload.end = this.formatDate(this.date[1])
      } else if (this.date && !Array.isArray(this.date)) {
        this.payload.start = this.formatDate(this.date)
        this.payload.end = this.formatDate(this.date)
      } else {
        this.payload.start = ''
        this.payload.end = ''
      }
      this.applyFilters()
    },

    formatDate(date) {
      if (!date) return ''
      let d = new Date(date)
      let month = '' + (d.getMonth() + 1)
      let day = '' + d.getDate()
      let year = d.getFullYear()

      if (month.length < 2) month = '0' + month
      if (day.length < 2) day = '0' + day

      return [year, month, day].join('-')
    },

    formatCurrency(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00'
      }

      const num = parseFloat(value)
      if (isNaN(num)) {
        return '0.00'
      }

      const absNum = Math.abs(num)
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })

      return num < 0 ? `(${formattedNum})` : formattedNum
    },

    exportToExcel() {
      if (!this.data?.length) {
        this.$swal.fire({
          icon: 'warning',
          title: 'No Data',
          text: 'No data available to export.'
        })
        return
      }

      try {
        this.isLoading = true

        // Prepare headers
        const headers = [
          'Phone Number',
          'Total Bets',
          'Total Odds',
          'Total Stake',
          'Total Win Amount',
          'Total Wins',
          'Total Deposits',
          'Total Withdrawals',
          'Net Gaming Revenue'
        ]

        // Prepare data rows
        const rows = this.data.map(item => [
          item.Phone_Number || '',
          item.Total_bets || 0,
          parseFloat(item.Total_odds || 0),
          parseFloat(item.Total_Stake || 0),
          parseFloat(item.Total_Win_amount || 0),
          item.Total_Wins || 0,
          parseFloat(item.Total_Deposits || 0),
          parseFloat(item.Total_Withdrawals || 0),
          parseFloat(item.NGR || 0)
        ])

        // Create worksheet
        const worksheet = XLSX.utils.aoa_to_sheet([headers, ...rows])

        // Format currency columns
        // Total_odds, Total_Stake, Total_Win_amount, Total_Deposits, Total_Withdrawals, NGR
        const currencyColumns = [2, 3, 4, 6, 7, 8] 
        const range = XLSX.utils.decode_range(worksheet['!ref'])
        
        for (let row = 1; row <= range.e.r; row++) {
          currencyColumns.forEach(col => {
            const cellAddress = XLSX.utils.encode_cell({ r: row, c: col })
            if (worksheet[cellAddress]) {
              worksheet[cellAddress].z = '#,##0.00'
            }
          })
        }

        // Set column widths
        worksheet['!cols'] = [
          { wch: 15 }, // Phone Number
          { wch: 12 }, // Total Bets
          { wch: 15 }, // Total Odds
          { wch: 15 }, // Total Stake
          { wch: 18 }, // Total Win Amount
          { wch: 12 }, // Total Wins
          { wch: 18 }, // Total Deposits
          { wch: 18 }, // Total Withdrawals
          { wch: 18 }  // NGR
        ]

        // Create workbook
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Greener Monday Report')

        // Generate filename
        const filename = `Greener_Monday_Report_${moment().format('YYYY-MM-DD')}.xlsx`

        // Download file
        XLSX.writeFile(workbook, filename)

        this.isLoading = false

        // Show success message
        this.$swal.fire({
          icon: 'success',
          title: 'Export Successful',
          text: `Successfully exported ${this.data.length} records to Excel.`,
          timer: 3000,
          showConfirmButton: false
        })

      } catch (error) {
        this.isLoading = false
        console.error('Excel export error:', error)
        
        this.$swal.fire({
          icon: 'error',
          title: 'Export Failed',
          text: 'An error occurred while exporting to Excel. Please try again.',
          confirmButtonText: 'OK'
        })
      }
    }
  }
}
</script>

<style scoped>
.filter-card {
  transition: border-color 0.2s ease;
}

.filter-card:hover {
  border-color: #6366f1;
}

/* Date picker styles */
:deep(.dp__input) {
  padding: 0.25rem 2rem;
  font-size: 0.75rem;
  line-height: 1rem;
  border-radius: 0.375rem;
}

:deep(.dp__main) {
  font-size: 0.75rem;
}
</style>