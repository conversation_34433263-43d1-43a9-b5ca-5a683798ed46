<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Sports" pageSubtitle="Fixtures" />

    <!-- Filter Section -->
    <div class="px-3 mb-4">
      <div class="bg-white rounded-lg shadow p-3">
        <div class="flex justify-between items-center mb-3">
          <h3 class="text-base font-medium text-gray-700">Filters</h3>
          <div class="flex gap-1">
            <button @click="performSearch()" class="px-3 py-1 text-sm bg-indigo-600 text-white rounded-md hover:bg-indigo-700 flex items-center">
              <i class="fas fa-search mr-1"></i> Search
            </button>
            <button @click="resetFilters()" class="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
              Reset
            </button>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
          <!-- Match Identification Group -->
          <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
            <h4 class="text-sm font-medium text-gray-700 mb-1">Match Identification</h4>
            <div class="space-y-2">
              <div>
                <label class="block text-xs text-gray-600 mb-1">Match ID</label>
                <input type="text" placeholder="Enter match ID"
                       class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                       v-model="moreParams.match_id"
                       @keyup.enter="performSearch()">
              </div>

              <div>
                <label class="block text-xs text-gray-600 mb-1">Game ID</label>
                <input type="text" placeholder="Enter game ID"
                       class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                       v-model="moreParams.game_id"
                       @keyup.enter="performSearch()">
              </div>

              <div>
                <label class="block text-xs text-gray-600 mb-1">Team Name</label>
                <input type="text" placeholder="Enter team name"
                       class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                       v-model="moreParams.team_name"
                       @keyup.enter="performSearch()">
              </div>
            </div>
          </div>

          <!-- Country, Tournament & Sport Group -->
          <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
            <h4 class="text-sm font-medium text-gray-700 mb-1">Country, Tournament & Sport</h4>
            <div class="space-y-2">
              <div>
                <label class="block text-xs text-gray-600 mb-1">Country</label>
                <select
                  class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                  v-model="country"
                  @change="onCountryChange($event.target.value)">
                  <option value="">All Countries</option>
                  <option v-for="(item, index) in countries" :key="index" :value="item">
                    {{ item.label }}
                  </option>
                </select>
              </div>

              <div>
                <label class="block text-xs text-gray-600 mb-1">Sport</label>
                <select
                  class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                  v-model="sport"
                  @change="onSportChange($event.target.value)">
                  <option value="">All Sports</option>
                  <option v-for="(item, index) in sports" :key="index" :value="item">
                    {{ item.label }}
                  </option>
                </select>
              </div>

              <div>
                <label class="block text-xs text-gray-600 mb-1">Tournament</label>
                <select
                  class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                  v-model="tournament"
                  @change="onTournamentChange($event.target.value)">
                  <option value="">All Tournaments</option>
                  <option v-for="(item, index) in tournaments" :key="index" :value="item">
                    {{ item.label }}
                  </option>
                </select>
              </div>

            </div>
          </div>

          <!-- Status & Coverage Group -->
          <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
            <h4 class="text-sm font-medium text-gray-700 mb-1">Status & Coverage</h4>
            <div class="space-y-2">
              <div>
                <label class="block text-xs text-gray-600 mb-1">Match Status</label>
                <select
                  class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                  v-model="match_status_select"
                  @change="handleMatchStatusChange">
                  <option value="">All Statuses</option>
                  <option v-for="(item, index) in match_status" :key="index" :value="item">
                    {{ item.text }}
                  </option>
                </select>
              </div>

              <div>
                <label class="block text-xs text-gray-600 mb-1">Coverage</label>
                <select
                  class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                  v-model="moreParams.live_coverage"
                  @change="performSearch()">
                  <option value="">All Coverage</option>
                  <option value="1">Live Games</option>
                  <option value="0">PreMatch Games</option>
                </select>
              </div>

              <div>
                <label class="block text-xs text-gray-600 mb-1">Live/Pre-match</label>
                <select
                  class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                  v-model="live_prematch_select"
                  @change="performSearch()">
                  <option v-for="(item, index) in live_or_prematch" :key="index" :value="item">
                    {{ item.text }}
                  </option>
                </select>
              </div>

            </div>
          </div>

          <!-- Date Range Group -->
          <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
            <h4 class="text-sm font-medium text-gray-700 mb-1">Date</h4>
            <div class="space-y-2">
              <div>
                <label class="block text-xs text-gray-600 mb-1">Select Date Range</label>
                <VueDatePicker
                  v-model="date"
                  range
                  multi-calendars
                  :enable-time-picker="false"
                  :format="'yyyy-MM-dd'"
                  placeholder="Select date range"
                  class="w-full text-xs"
                  @update:model-value="selectDate"
                />
              </div>

            </div>
          </div>

        </div>
      </div>
    </div>

   <!-- Replace the table with DataTable component -->
   <auto-table
   class="mx-3"
      :headers="tableHeaders"
      :data="fixtures"
      :has-actions="true"
      :get-actions="getRowActions"
      :total-items="total"
      :items-per-page="limit"
      :current-page-prop="offset"
      :server-side-pagination="true"
      :pagination="total > limit"
      :show-items-count="true"
      :items-per-page-options="[10, 25, 50, 100]"
      @page-change="handlePageChange"
      @items-per-page-change="handleLimitChange"
    >
      <!-- Index Column -->
      <template #index="{ index }">
        <div class="text-center">{{ index + 1 + ((offset - 1) * limit) }}</div>
      </template>

      <!-- Match ID Column -->
      <template #match_id="{ item }" :class="{' bg-green-100 ': isRecentlyUpdated(item.date)}">
        <div class="text-center" :class="{ 'ring-1 ring-blue-700 animate-pulse': isRecentlyUpdated(item.match_id) }">
          {{ item.match_id }}
        </div>
      </template>

      <!-- Game ID Column -->
      <template #game_id="{ item }">
        <div class="text-center">{{ item.game_id }}</div>
      </template>

      <!-- Country Column -->
      <template #country="{ item }">
        <div >Country: {{ item.country }}</div>
        <div>Competition: {{ item.category_name }}</div>
        <div>Sport: {{ item.sport_name }}</div>
      </template>



      <!-- Teams Column -->
      <template #teams="{ item }">
        <div class="px-2 py-1 text-center bg-green-100 text-green-800 rounded-full text-xs font-medium">
          {{ item.competitor1.length > 30 ? item.competitor1.substring(0, 30) + '...' : item.competitor1 }}
        </div>
        <div class=" text-center">vs</div>
        <div class="px-2 py-1 text-center bg-red-100 text-red-800 rounded-full text-xs font-medium">
          {{ item.competitor2.length > 30 ? item.competitor2.substring(0, 30) + '...' : item.competitor2 }}
        </div>
      </template>

      <!-- Scores Column -->
      <template #scores="{ item }">
        <div class="text-center font-bold">{{ item.home_score ?? '-' }} : {{ item.away_score ?? '-' }}</div>
      </template>

      <!-- Status Column -->
      <template #status="{ item }">
        <div class="text-center">
          <span v-if="item.pre_match_coverage==='1' && item.live_coverage===null"
                class="px-5 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
            Pre-match
          </span>
          <span v-else-if="item.live_coverage==='1'"
                class="px-5 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
            Live
          </span>
          <span v-else class="px-5 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">
            -
          </span>
        </div>

        <div class="text-center pt-2" style="font-size: 11px; color: grey">{{ moment(item.date).format('llll') }}</div>
      </template>

      <!-- Date Column -->
      <template #date="{ item }">
        <div class="text-center" style="font-size: 11px; color: grey">{{ moment(item.date).format('llll') }}</div>
      </template>

      <!-- Created Column -->
      <!-- <template #created="{ item }">
        <div class="text-center">{{ moment(item.created).format('llll') }}</div>
      </template> -->

      <!-- Actions Column -->
      <template #actions="{ item }">
        <action-dropdown button-text="Actions" :show-text="false">
          <action-item text="Set Priority" color="blue" @click="openPriorityModal(item)">
            <template #icon>
              <svg class="mr-3 h-5 w-5 text-blue-500 group-hover:text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
              </svg>
            </template>
          </action-item>

          <action-item v-if="item.live_coverage === '0'||item.live_coverage === null"
            text="Turn Live Coverage ON" color="green" @click="turnLiveCoverageOnOff('1', item.match_id)">
            <template #icon>
              <svg class="mr-3 h-5 w-5 text-green-500 group-hover:text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </template>
          </action-item>

          <action-item v-else text="Turn Live Coverage OFF" color="red" @click="turnLiveCoverageOnOff('0', item.match_id)">
            <template #icon>
              <svg class="mr-3 h-5 w-5 text-green-500 group-hover:text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </template>
          </action-item>

          <action-item v-if="item.pre_match_coverage === '0'||item.live_coverage === null"
            text="Turn Pre-match Coverage ON" color="green" @click="turnPreMatchCoverageOnOff('1', item.match_id)">
            <template #icon>
              <svg class="mr-3 h-5 w-5 text-green-500 group-hover:text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </template>
          </action-item>

          <action-item v-else text="Turn Pre-match Coverage OFF" color="red" @click="turnPreMatchCoverageOnOff('0', item.match_id)">
            <template #icon>
              <svg class="mr-3 h-5 w-5 text-red-500 group-hover:text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
              </svg>
            </template>
          </action-item>

          <!-- <action-item text="View Odds Live" color="green" @click="viewOddsLive(item)">
            <template #icon>
              <svg class="mr-3 h-5 w-5 text-green-500 group-hover:text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </template>
          </action-item>
           -->
        </action-dropdown>
      </template>
    </auto-table>


        <!-- Modal -->
      <div v-if="isPriorityModalOpen"
           class="fixed inset-0 z-10 bg-black bg-opacity-50 flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
          <h2 class="text-lg font-semibold mb-4">Set Priority</h2>

          <!-- Input Fields -->
          <div class="mb-4">
            <label for="field1" class="block text-sm font-medium text-gray-700">
              Highlights Priority ({{ fixture.highlights_priority }})
            </label>
            <input
                type="number"
                id="highlights_priority"
                v-model="updateFixtureParams.highlights_priority"
                class="mt-1 block w-full border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                placeholder="Enter value"/>
          </div>
          <div class="mb-4">
            <label for="field2" class="block text-sm font-medium text-gray-700">
              Fixture Priority ({{ fixture.fixture_priority }})
            </label>
            <input type="number"
                   id="fixture_priority"
                   v-model="updateFixtureParams.fixture_priority"
                   class="mt-1 block w-full border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                   placeholder="Enter value"/>
          </div>

          <div class="mb-4">
            <label for="field2" class="block text-sm font-medium text-gray-700">
              Boosted Priority ({{ fixture.boosted_priority }})
            </label>
            <input type="number"
                   id="boosted_priority"
                   v-model="updateFixtureParams.boosted_priority"
                   class="mt-1 block w-full border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                   placeholder="Enter value"/>
          </div>

          <div class="mb-4">
            <label for="upcoming_priority" class="block text-sm font-medium text-gray-700">
              Upcoming Priority ({{ fixture.upcoming_priority }})
            </label>
            <input type="number"
                   id="upcoming_priority"
                   v-model="updateFixtureParams.upcoming_priority"
                   class="mt-1 block w-full border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                   placeholder="Enter value"/>
          </div>

          <!-- Modal Buttons -->
          <div class="flex justify-end space-x-4">
            <button @click="closeModal()" class="px-4 py-2 bg-gray-300 rounded-md hover:bg-gray-400">
              Cancel
            </button>
            <button @click="updateFix" class="px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600">
              Save
            </button>
          </div>
        </div>
      </div>

  </div>
</template>

<script>
import moment from "moment";
import { mapActions } from "vuex";
import { endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths } from "date-fns";
import VueDatePicker from "@vuepic/vue-datepicker";
import InputField from "@/components/InputField.vue";
import { AutoTable, ActionDropdown, ActionItem, CustomLoading } from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  components: {
    AutoTable, // Replace with your custom component
    InputField,
    VueDatePicker,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    PageHeader,
  },
  data() {
    return {
      tableHeaders: [
        { key: "index", label: "#", align: "center" },
        { key: "match_id", label: "Match ID", align: "center" },
        { key: "game_id", label: "Game ID", align: "center" },
        // { key: "sport_name", label: "Sports Name", align: "center" },
        { key: "country", label: "Country", align: "left" },
        { key: "teams", label: "Teams", align: "center" },
        { key: "scores", label: "Scores", align: "center" },
        { key: "status", label: "Match Status", align: "center" },
        // { key: "date", label: "Start Time", align: "center" },
        // { key: "created", label: "Created On", align: "center" }
      ],
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 100,
      offset: 1,
      showDropdown: [],
      viewModelOpen: false,
      //
      selectedStatus: 'All', // Initially, no filter (show all)
      //
      fixture: {},
      fixtures: [],
      moreParams: {
        team_name: '',
        sport_id: '',
        match_id: '',
        game_id: '',
        tournament_id: '',
        is_live: '',
        live_coverage: '',
        pre_match_coverage: '',
        country: '',
        status: '',
        sort: '',
        play_date: '',
        start: '',
        end: '',
        page: '',
        limit: "100",
        timestamp: 'timestamp',
        skip_cache: '',
      },
      updateFixtureParams: {
        live_coverage: "",
        pre_match_coverage: "",
        highlights_priority: "",
        fixture_priority: "",
        boosted_priority: "",
        timestamp: "$timestamp",
      },

      //
      country: '',
      countries: [],
      countryParams: {
        timestamp: 'timestamp',
        limit: '1000',
        sort: '',
        start: '',
        end: '',
        page: '',
      },
      //
      sport: '',
      sports: [],
      sportsParams: {
        sport_id: '',
        sport_name: '',
        priority: '',
        timestamp: 'timestamp',
        limit: '1000000',
        sort: '',
        start: '',
        end: '',
        page: '',
      },
      //
      tournament: '',
      tournaments: [],
      tournamentsParams: {
        sport_id: '',
        category_id: '',
        country: '',
        priority: '',
        timestamp: 'timestamp',
      },

      live_prematch_select: {value:'',text:'All Types'},
      live_or_prematch:
          [
            {text: 'All Types', value: ''},
            {text: 'Live', value: 1},
            {text: 'Pre-Match', value: 0},
          ],

      match_status_select: {},
      match_status:
          [
            {text: 'All', value: ""},
            {text: 'Not Started', value: "NotStarted"},
            {text: 'In Progress', value: "InProgress"},
            {text: 'Finished', value: "Finished"},
          ],
      //
      date: null,
      presetRanges: [
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
        {
          label: 'This year (slot)',
          range: [startOfYear(new Date()), endOfYear(new Date())],
          slot: 'yearly',
        },
      ],

      //
      isPriorityModalOpen: false,
      field1: null,
      field2: null,
      createdDateRange: null,
      recentlyUpdatedIds: [],
      highlightDuration: 30000, // 30 seconds in milliseconds
    }
  },
  mounted() {
    this.setFixtures()
    this.setCountries()
    this.setSports()
    this.setTournaments()

    // For testing, manually add a match ID to highlight
    // Remove this in production
    setTimeout(() => {
      if (this.fixtures.length > 0) {
        this.recentlyUpdatedIds = [this.fixtures[0].match_id];
        console.log("Manually highlighting:", this.recentlyUpdatedIds);
      }
    }, 2000);
  },
  watch: {
    //
    async live_prematch_select(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.moreParams.is_live = newVal.value
        await this.setFixtures()
      }
    }
  },
  methods: {
    ...mapActions(["getFixtures", "updateFixture", "getCountries", "getSports", "getTournaments", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    // Get row actions for AutoTable
    getRowActions(item) {
      const actions = [
        {
          label: 'Edit Priority',
          action: () => this.openPriorityModal(item),
          icon: 'fas fa-edit'
        }
      ];

      if (item.status === '1') {
        actions.push({
          label: 'Disable',
          action: () => this.activateDeActivate(item, '0'),
          icon: 'fas fa-times-circle'
        });
      } else {
        actions.push({
          label: 'Enable',
          action: () => this.activateDeActivate(item, '1'),
          icon: 'fas fa-check-circle'
        });
      }

      return actions;
    },

    // Handle items per page change
    handleLimitChange(newLimit) {
      this.limit = newLimit;
      this.moreParams.limit = newLimit.toString();
      this.offset = 1;
      this.moreParams.page = '1';
      this.setFixtures();
    },
    toggleDropdown(index) {
      console.log("index : ",index)
      // Close all other dropdowns and toggle the selected one
      this.showDropdown = this.showDropdown.map((value, i) => (i === index ? !value : false));
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    async performSearch() {
      this.moreParams.page = 1; // Reset to first page when searching
      await this.setFixtures();
    },

    async onCountryChange(value) {
      if (!value) {
        this.moreParams.country = ''
        this.tournamentsParams.country = ''
        this.moreParams.page = 1
        await this.setFixtures()
        await this.setTournaments()
      } else {
        this.moreParams.country = value.label ?? this.country.label
        this.tournamentsParams.country = value.label ?? this.country.label
        this.moreParams.page = 1
        await this.setFixtures()
        await this.setTournaments()
      }
    },

    async onSportChange(value) {
      if (!value) {
        this.moreParams.sport_id = ''
        this.tournamentsParams.sport_id = ''
        this.moreParams.page = 1
        await this.setFixtures()
        await this.setTournaments()
      } else {
        this.moreParams.sport_id = value.value ?? this.sport.value
        this.tournamentsParams.sport_id = value.value ?? this.sport.value
        this.moreParams.page = 1
        await this.setFixtures()
        await this.setTournaments()
      }
    },

    async onTournamentChange(value) {
      if (!value) {
        this.moreParams.tournament_id = ''
        this.moreParams.page = 1
        await this.setFixtures()
      } else {
        this.moreParams.tournament_id = value.value ?? this.tournament.value
        this.moreParams.page = 1
        await this.setFixtures()
      }
    },

    async selectDate() {
      let vm = this

      // If date is null (cleared), reset date filters and fetch data
      if (!this.date) {
        console.log('Date filter cleared, resetting and fetching data...');
        vm.moreParams.start = '';
        vm.moreParams.end = '';
        vm.moreParams.timestamp = Date.now();
        vm.moreParams.play_date = '';
        await vm.setFixtures();
        return;
      }

      // If date range is incomplete, return without doing anything
      if (!this.date[0] || !this.date[1]) return;

      // Update date filter values
      vm.moreParams.start = vm.formatDate(this.date[0]);
      vm.moreParams.end = vm.formatDate(this.date[1]);
      vm.moreParams.timestamp = Date.now();
      vm.moreParams.play_date = vm.formatDate(this.date[0]) + ' ' + vm.formatDate(this.date[1]);

      // Log that date filter was updated and fetch data
      console.log('Date filter updated, fetching data with new date range...');
      await vm.setFixtures();
    },


    async selectDateStart() {
      let vm = this
      vm.moreParams.timestamp = Date.now()
      vm.moreParams.play_date = vm.formatDate(this.date[0]) + ' ' + vm.formatDate(this.date[1])

      await vm.setFixtures()
    },

    async handleMatchStatusChange() {
      console.log("Match Status changed:", this.match_status_select);
      // Call your function or trigger any logic here

      this.moreParams.status = this.match_status_select.value
      await this.setFixtures()
    },

    formatDate(date) {
      var d = new Date(date),
          month = '' + (d.getMonth() + 1),
          day = '' + d.getDate(),
          year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    // Pagination and dropdowns
    handlePageChange(page) {
      this.offset = page;
      this.moreParams.page = page;
      this.setFixtures();
    },
    gotToPage(page) {
      this.handlePageChange(page);
    },
    toggleDropdownp(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    toggleDetails(data) {
      this.viewModelOpen = true;
      this.deposit = data;
    },

    openPriorityModal(data, index) {
      this.isPriorityModalOpen = true;
      this.fixture = data
      console.log("openPriorityModal data: ", JSON.stringify(data))
      this.updateFixtureParams.highlights_priority = parseInt(data.highlights_priority)
      this.updateFixtureParams.fixture_priority = parseInt(data.fixture_priority)
      this.updateFixtureParams.boosted_priority = parseInt(data.boosted_priority)
      this.updateFixtureParams.match_id = data.match_id
      this.showDropdown[index] = false
    },

    closeModal() {
      this.isPriorityModalOpen = false;
    },

    async setFixtures() {
      let app = this
      app.isLoading = true

      const params = new URLSearchParams();

      for (const key in app.moreParams) {
        if (app.moreParams.hasOwnProperty(key)) {
          params.append(key, app.moreParams[key]);
        }
      }
      const queryString = params.toString();
      // console.log("Params: " + queryString);
      let response = await this.getFixtures(queryString)
      // console.log("Fixtures OK: " + JSON.stringify(response))
      if (response.status === 200) {
        // Check for updated records
        const currentTime = Date.now();
        const updatedRecords = response.message.result.filter(fixture => {
          const updatedTime = new Date(fixture.updated).getTime();
          // console.log(`updatedTime: ${updatedTime} :: currentTime: ${currentTime} :: highlightDuration: ${this.highlightDuration}`);
          return (currentTime - updatedTime) < this.highlightDuration;
        });

        // Store the match_ids of recently updated records
        this.recentlyUpdatedIds = updatedRecords.map(fixture => fixture.match_id);

        app.fixtures = response.message.result;

        if (response.message.record_count !== 0) {
          app.total = parseInt(response.message.record_count)
        }

        app.showDropdown = []
        for (let i = 0; i < app.fixtures.length; i++) {
          app.showDropdown.push(false)
        }
      } else {
        app.fixtures = [];
        app.total = 0;
      }
      app.isLoading = false
    },

    async setCountries() {
      let app = this
      app.isLoading = true

      const params = new URLSearchParams();

      for (const key in app.countryParams) {
        if (app.countryParams.hasOwnProperty(key)) {
          params.append(key, app.countryParams[key]);
        }
      }
      const queryString = params.toString();
      // console.log("Params: " + queryString);
      let response = await this.getCountries(queryString)

      // console.log("Countries OK: " + JSON.stringify(response))
      if (response.status === 200) {

        response.message.result.forEach(function (item) {
          let list = {label: item.country, value: parseInt(item.id)}
          app.countries.push(list)
        })
      } else {
        app.countries = [];
      }
      app.isLoading = false
    },

    async setSports() {
      let app = this
      app.isLoading = true

      const params = new URLSearchParams();

      for (const key in app.sportsParams) {
        if (app.sportsParams.hasOwnProperty(key)) {
          params.append(key, app.sportsParams[key]);
        }
      }
      const queryString = params.toString();
      // console.log("Params: " + queryString);
      let response = await this.getSports(queryString)
      // console.log("Sports OK: " + JSON.stringify(response))
      if (response.status === 200) {

        response.message.result.forEach(function (item) {
          let list = {label: item.sport_name, value: parseInt(item.sport_id)}
          app.sports.push(list)
        })
      } else {
        app.sports = [];
      }
      // app.filterByStatus()
      app.isLoading = false
    },

    async setTournaments() {
      let app = this
      app.isLoading = true
      app.tournamentsParams.limit = 10000
      const params = new URLSearchParams();
      for (const key in app.tournamentsParams) {
        if (app.tournamentsParams.hasOwnProperty(key)) {
          params.append(key, app.tournamentsParams[key]);
        }
      }
      const queryString = params.toString();
      let response = await this.getTournaments(queryString)
      if (response.status === 200) {
        app.tournaments = []
        response.message.result.forEach(function (item) {
          let list = {label: item.tournament_name, value: parseInt(item.tournament_id)}
          app.tournaments.push(list)
        })

      } else {
        app.tournaments = []
      }
      app.isLoading = false
    },

    async viewOddsLive(data) {
      // route to odds_live
      await this.$router.push({name: 'odds-live', params: data.match_id})
    },

    async turnLiveCoverageOnOff(num, match_id) {
      console.log('turnLiveCoverageOnOff:: ', num, ' :: match_id: ', match_id);
      this.updateFixtureParams.match_id = match_id;
      this.updateFixtureParams.live_coverage = num;
      await this.updateFix();
    },

    async turnPreMatchCoverageOnOff(num, match_id) {
      console.log('turnPreMatchCoverageOnOff:: ', num, ' :: match_id: ', match_id);
      this.updateFixtureParams.match_id = match_id;
      this.updateFixtureParams.pre_match_coverage = num;
      await this.updateFix();
    },

    async updateFix() {
      let app = this
      app.isLoading = true

      console.log("updateFixtureParams: ", JSON.stringify(app.updateFixtureParams))

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this update this Fixture!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (_) => {
          return await this.updateFixture(app.updateFixtureParams)
        },
      })
          .then(async (response) => {
            app.isLoading = false

            if (response.value.status === 200) {
              app.$swal.fire({
                title: 'Updated!',
                text: response.value.message,
                icon: 'success'
              }).then(async (_) => {

                await app.setFixtures()
              })

            } else {
              app.$swal.fire('Error!', response.message, 'error')
            }
          })

      app.closeModal()

    },
    filterByCreatedDateRange() {
      if (this.createdDateRange && this.createdDateRange.length === 2) {
        this.moreParams.start = this.formatDate(this.createdDateRange[0]);
        this.moreParams.end = this.formatDate(this.createdDateRange[1]);
        this.moreParams.page = 1;
        this.setFixtures();
      }
    },
    clearCreatedDateRangeFilter() {
      this.createdDateRange = null;
      if (this.moreParams.start || this.moreParams.end) {
        delete this.moreParams.start;
        delete this.moreParams.end;
        this.moreParams.page = 1;
        this.setFixtures();
      }
    },
    isRecentlyUpdated(matchId) {
      return this.recentlyUpdatedIds.includes(matchId);
    },
  },
}
</script>


<style scoped>


/* Date picker styles */
:deep(.dp__input) {
  padding: 0.25rem 2rem;
  font-size: 0.75rem;
  line-height: 1rem;
  border-radius: 0.375rem;
}

:deep(.dp__main) {
  font-size: 0.75rem;
}
</style>
