<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />
   
    <page-header pageName="Sports" pageSubtitle="Manual Resulting" />


    <!-- Filter Section -->
    <div class="px-3 mb-4">
      <div class="bg-white rounded-lg shadow p-3">
        <div class="flex justify-between items-center mb-3">
          <h3 class="text-base font-medium text-gray-700">Filters</h3>
          <div class="flex gap-1">
            <button @click="setFixturesManualResult()" class="px-3 py-1 text-xs bg-indigo-600 text-white rounded-md hover:bg-indigo-700 flex items-center">
              <i class="fas fa-search mr-1"></i> Search
            </button>
            <button @click="resetFilters()" class="px-3 py-1 text-xs bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
              Reset
            </button>
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
          <!-- Match Identification Group -->
          <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
            <h4 class="text-sm font-medium text-gray-700 mb-1">Match Identification</h4>
            <div class="space-y-2">
              <div>
                <label class="block text-xs text-gray-600 mb-1">Match ID</label>
                <input type="text" placeholder="Enter match ID" 
                       class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                       v-model="moreParams.match_id"
                       @keyup.enter="setFixturesManualResult()">
              </div>
              <div>
                <label class="block text-xs text-gray-600 mb-1">Game ID</label>
                <input type="text" placeholder="Enter game ID" 
                       class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                       v-model="moreParams.game_id"
                       @keyup.enter="setFixturesManualResult()">
              </div>
            </div>
          </div>
          
          <!-- Team & Tournament Group -->
          <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
            <h4 class="text-sm font-medium text-gray-700 mb-1">Team & Tournament</h4>
            <div class="space-y-2">
              <div>
                <label class="block text-xs text-gray-600 mb-1">Team Name</label>
                <input type="text" placeholder="Enter team name" 
                       class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                       v-model="moreParams.team_name"
                       @keyup.enter="setFixturesManualResult()">
              </div>
              <div>
                <label class="block text-xs text-gray-600 mb-1">Tournament</label>
                <select
                  class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                  v-model="tournament"
                  @change="onTournamentChange($event.target.value)">
                  <option value="">All Tournaments</option>
                  <option v-for="(item, index) in tournaments" :key="index" :value="item">
                    {{ item.label }}
                  </option>
                </select>
              </div>
            </div>
          </div>
          
          <!-- Sport & Country Group -->
          <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
            <h4 class="text-sm font-medium text-gray-700 mb-1">Sport & Country</h4>
            <div class="space-y-2">
              <div>
                <label class="block text-xs text-gray-600 mb-1">Sport</label>
                <select
                  class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                  v-model="sport"
                  @change="onSportChange($event.target.value)">
                  <option value="">All Sports</option>
                  <option v-for="(item, index) in sports" :key="index" :value="item">
                    {{ item.label }}
                  </option>
                </select>
              </div>
              <div>
                <label class="block text-xs text-gray-600 mb-1">Country</label>
                <select
                  class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                  v-model="country"
                  @change="onCountryChange($event.target.value)">
                  <option value="">All Countries</option>
                  <option v-for="(item, index) in countries" :key="index" :value="item">
                    {{ item.label }}
                  </option>
                </select>
              </div>
            </div>
          </div>
          
          <!-- Date Range & Status Group -->
          <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
            <h4 class="text-sm font-medium text-gray-700 mb-1">Date & Status</h4>
            <div class="space-y-2">
              <div>
                <label class="block text-xs text-gray-600 mb-1">Status</label>
                <select
                  class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                  v-model="match_status_select"
                  @change="handleMatchStatusChange">
                  <option value="">All Statuses</option>
                  <option v-for="(item, index) in match_statuses" :key="index" :value="item">
                    {{ item.text }}
                  </option>
                </select>
              </div>
              <div>
                <label class="block text-xs text-gray-600 mb-1">Select Date Range</label>
                <VueDatePicker
                  v-model="date"
                  range
                  multi-calendars
                  :enable-time-picker="false"
                  :format="'yyyy-MM-dd'"
                  placeholder="Select date range"
                  class="w-full text-xs"
                  @update:model-value="selectDate"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Data Table with Highlighting -->
    <auto-table class="mx-3"
      :headers="tableHeaders"
      :data="fixtures"
      :has-actions="true"
      :total-items="total"
      :items-per-page="limit"
      :current-page-prop="offset"
      :server-side-pagination="true"
      :pagination="total > limit"
      :show-items-count="true"
      :auto-size-columns="true"
      :table-margin-bottom="16"
      @page-change="gotToPage"
    >
      <!-- Row Highlighting -->
      <template #row="{ item }">
        <tr :class="{ 'bg-yellow-50 transition-colors duration-500': isRecentlyUpdated(item.match_id) }">
          <slot name="row-content" :item="item"></slot>
        </tr>
      </template>

      <!-- Match ID Column -->
      <template #match_id="{ item }">
        <div class="flex items-center">
          <span class="font-medium">{{ item.match_id }}</span>
          <span v-if="isRecentlyUpdated(item.match_id)" 
                class="ml-2 px-2 py-0.5 text-xs bg-yellow-200 text-yellow-800 rounded-full">
            Updated
          </span>
        </div>
      </template>

      <!-- Game ID Column -->
      <template #game_id="{ item }">
        <span>{{ item.game_id }}</span>
      </template>

      <!-- Sport and Country Name Column -->
      <template #sport_name="{ item }">
        <span>{{ item.sport_name }}</span>
        <br>
        <span>{{ item.country }}</span>
      </template>

      <!-- Teams Column -->
      <template #name="{ item }">
        <div class="px-2 py-1 text-center bg-green-100 text-green-800 rounded-full text-xs font-medium">
          {{ item.name.split("v")[0] }}
        </div>
        <div class=" text-center">vs</div>
        <div class="px-2 py-1 text-center bg-red-100 text-red-800 rounded-full text-xs font-medium">
          {{ item.name.split("v")[1] }}
        </div>
      </template>

      <!-- Scores Column -->
      <template #scores="{ item }">
        <div class="flex items-center space-x-2">
          <div class="text-center px-2 py-1 bg-gray-100 rounded">
            <span class="font-bold">{{ item.home_score ?? '-' }}</span>
            <span class="text-xs text-gray-500 mx-1">:</span>
            <span class="font-bold">{{ item.away_score ?? '-' }}</span>
          </div>
          
        </div>
      </template>

      <!-- Start Time Column -->
      <template #game_start_time="{ item }">
        <span class="text-xs text-gray-600">{{ moment(item.game_start_time).format('llll') }}</span>
      </template>

      <!-- BetSlip Count Column -->
       <template #bet_slip_count="{ item }">
        <div class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
          {{ item.bet_slip_count || 0 }}
        </div>
      </template>

      <!-- Created Date Column -->
      <template #created_at="{ item }">
        <span class="text-xs text-gray-600">{{ moment(item.created_at).format('llll') }}</span>
      </template>

      <!-- Actions Column -->
      <template #actions="{ item }">
        <action-dropdown button-text="Actions" :show-text="false">
          
          <action-item 
            text="Update Scores" color="green" @click="openPriorityModal(item)">
            <template #icon>
              <svg class="mr-3 h-5 w-5 text-green-500 group-hover:text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </template>
          </action-item>

          <!-- <action-item
            text="View BetSlip Count" color="green" @click="viewBetSlipCount(item)">
            <template #icon>
              <svg class="mr-3 h-5 w-5 text-green-500 group-hover:text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </template>
          </action-item> -->
          
        </action-dropdown>
      </template>

  
    </auto-table>

      <!-- Modal -->
      <div v-if="isPriorityModalOpen"
           class="fixed inset-0 z-10 bg-black bg-opacity-50 flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
          <h2 class="text-lg font-semibold mb-4">
            Set Resulting Details
            <br>
            ({{ fixture.name }})
          </h2>

          <!-- Input Fields -->
          <div class="grid grid-cols-3 gap-4">
            <div class="block mb-4">
              <label for="htScore" class="block text-sm font-medium text-gray-700">
                Halftime Score
              </label>
              <div class="grid grid-cols-2 gap-3 pr-5">
                <input type="text"
                       id="htScore"
                       v-model="ht.home"
                       class="mt-1 block text-center border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                       placeholder="0"/>
                <input type="text"
                       id="htScore"
                       v-model="ht.away"
                       class="mt-1 block text-center border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                       placeholder="0"/>
              </div>
            </div>

            <div class="mb-4">
              <label for="ftScore" class="block text-sm font-medium text-gray-700">
                Full Time Score
              </label>
              <div class="grid grid-cols-2  gap-3 pr-5">
                <input type="text"
                       id="htScore"
                       v-model="ft.home"
                       class="mt-1 block text-center border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                       placeholder="0"/>
                <input type="text"
                       id="htScore"
                       v-model="ft.away"
                       class="mt-1 block text-center border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                       placeholder="0"/>
              </div>
            </div>

            <div class="mb-4">
              <label for="ftScore" class="block text-sm font-medium text-gray-700">
                Corners
              </label>
              <div class="grid grid-cols-2 gap-3 pr-5">
                <input type="text"
                       id="htScore"
                       v-model="corners.home"
                       class="mt-1 block text-center border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                       placeholder="0"/>
                <input type="text"
                       id="htScore"
                       v-model="corners.away"
                       class="mt-1 block text-center border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                       placeholder="0"/>
              </div>
            </div>
          </div>

          <div class="border-t my-2"/>

          <!-- Input Fields -->
          <div class="grid grid-cols-3 gap-4">
            <div class="mb-4">
              <label for="htScore" class="block text-sm font-medium text-gray-700">
                Yellow Cards
              </label>
              <div class="grid grid-cols-2 gap-3 pr-5">
                <input type="text"
                       id="htScore"
                       v-model="yellowCards.home"
                       class="mt-1 block text-center border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                       placeholder="0"/>
                <input type="text"
                       id="htScore"
                       v-model="yellowCards.away"
                       class="mt-1 block text-center border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                       placeholder="0"/>
              </div>
            </div>

            <div class="mb-4">
              <label for="ftScore" class="block text-sm font-medium text-gray-700">
                Red Cards
              </label>
              <div class="grid grid-cols-2  gap-3 pr-5">
                <input type="text"
                       id="htScore"
                       v-model="redCards.home"
                       class="mt-1 block text-center border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                       placeholder="0"/>
                <input type="text"
                       id="htScore"
                       v-model="redCards.away"
                       class="mt-1 block text-center border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                       placeholder="0"/>
              </div>
            </div>

            <div class="mb-4">
              <label for="ftScore" class="block text-sm font-medium text-gray-700">
                Extra Time Score
              </label>
              <div class="grid grid-cols-2 gap-3 pr-5">
                <input type="text"
                       id="htScore"
                       v-model="et.home"
                       class="mt-1 block text-center border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                       placeholder="0"/>
                <input type="text"
                       id="htScore"
                       v-model="et.away"
                       class="mt-1 block text-center border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                       placeholder="0"/>
              </div>
            </div>
          </div>

          <div class="border-t my-2"/>

          <!-- Input Fields -->
          <div class="grid grid-cols-3 gap-4">
            <div class="mb-4">
              <label for="htScore" class="block text-sm font-medium text-gray-700">
                Penalties Score
              </label>
              <div class="grid grid-cols-2 gap-3 pr-5">
                <input type="text"
                       id="htScore"
                       v-model="penaltiesScore.home"
                       class="mt-1 block text-center border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                       placeholder="0"/>
                <input type="text"
                       id="htScore"
                       v-model="penaltiesScore.away"
                       class="mt-1 block text-center border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                       placeholder="0"/>
              </div>
            </div>

            <div class="mb-4">

            </div>

            <div class="mb-4 flex justify-end items-end">
              <button @click="updateFix('void')"
                      class="px-4 py-2 mr-3 bg-indigo-500 text-white rounded-md hover:bg-indigo-600"
                      style="">
                Void
              </button>
            </div>
          </div>

          <div class="border-t my-2"/>

          <!-- Modal Buttons -->
          <div class="flex justify-end space-x-4">
            <button @click="closeModal()" class="px-4 py-2 bg-gray-300 rounded-md hover:bg-gray-400">
              Cancel
            </button>
            <button @click="updateFix(null)" class="px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600">
              Save Results
            </button>
          </div>
        </div>
      </div>

      <!-- BetSlip Count Modal -->
      <div v-if="isBetSlipCountModalOpen"
           class="fixed inset-0 z-10 bg-black bg-opacity-50 flex items-center justify-center">
        <div class="bg-white rounded-lg p-6 w-1/3">
          <h2 class="text-2xl font-bold mb-4">BetSlip Count</h2>
          <p class="text-lg">Total BetSlip Count: {{ bet_slip_count }}</p>
          <div class="flex justify-end mt-4">
            <button @click="isBetSlipCountModalOpen = false"
                    class="px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600">
              Close
            </button>
          </div>
        </div>
      </div>
      
  </div>
</template>

<script>
import moment from "moment";
import {mapActions} from "vuex";
import {endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths} from "date-fns";
import VueDatePicker from "@vuepic/vue-datepicker";
import Swal from 'sweetalert2';
import {AutoTable, ActionDropdown, ActionItem, CustomLoading} from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  components: {
    AutoTable,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    PageHeader,
    VueDatePicker,
    Swal,
  },
  data() {
    return {
      // Add highlighting properties
      recentlyUpdatedIds: [],
      highlightDuration: 1800000, // 30 minutes in milliseconds
      
      // Add match statuses options
      match_statuses: [
        { value: "not_started", text: "Not Started" },
        { value: "in_progress", text: "In Progress" },
        { value: "finished", text: "Finished" },
        { value: "cancelled", text: "Cancelled" },
        { value: "postponed", text: "Postponed" },
        { value: "abandoned", text: "Abandoned" },
        { value: "interrupted", text: "Interrupted" },
        { value: "suspended", text: "Suspended" }
      ],

      tableHeaders: [
        { key: "match_id", label: "Match ID", align: "center" },
        { key: "game_id", label: "Game ID", align: "center" },
        { key: "sport_name", label: "Sports Name", align: "center" },
        { key: "category_name", label: "Competition", align: "center" },
        { key: "name", label: "Teams", align: "center" },
        { key: "scores", label: "Scores", align: "center" },
        { key: "bet_slip_count", label: "Affected Betslips", align: "center" },
        { key: "game_start_time", label: "Start Time", align: "center" },
      ],
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 100,
      offset: 1,
      showDropdown: [],
      viewModelOpen: false,
      //
      fixture: {},
      fixtures: [],
      moreParams: {
        team_name: '',
        sport_id: '',
        match_id: '',
        game_id: '',
        tournament_id: '',
        live_coverage: '',
        pre_match_coverage: '',
        country: '',
        status: '',
        sort: '',
        start_time: '',
        start: '',
        end: '',
        page: '',
        limit: "100",
        timestamp: 'timestamp',
        skip_cache: '',
      },

      //
      // updateData: {
      ht: {
        home: '',
        away: '',
      },
      ft: {
        home: '',
        away: '',
      },
      et: {
        home: '',
        away: '',
      },
      corners: {
        home: '',
        away: '',
      },
      yellowCards: {
        home: '',
          away: '',
        },
        redCards: {
          home: '',
          away: '',
        },
        penaltiesScore: {
          home: '',
          away: '',
        },
      // },
      updateFixtureParams: {
        half_time_scores: '',
        full_time_scores: '',
        extra_time_scores: '',
        extra_results: '',
        resulting_type: 'Manual',
      },

      //
      country: '',
      countries: [],
      countryParams: {
        timestamp: 'timestamp',
        limit: '1000',
        sort: '',
        start: '',
        end: '',
        page: '',
      },
      //
      sport: '',
      sports: [],
      sportsParams: {
        sport_id: '',
        sport_name: '',
        priority: '',
        timestamp: 'timestamp',
        limit: '1000000',
        sort: '',
        start: '',
        end: '',
        page: '',
      },
      //
      tournament: '',
      tournaments: [],
      tournamentsParams: {
        sport_id: '',
        category_id: '',
        country: '',
        priority: '',
        timestamp: 'timestamp',
      },

      live_prematch_select: {},
      live_or_prematch:
          [
            {text: 'Live', value: 1},
            {text: 'Pre-Match', value: 0},
          ],

      match_status_select: {},
      match_status:
          [
            {text: 'All', value: ""},
            {text: 'Not Started', value: "NotStarted"},
            {text: 'In Progress', value: "InProgress"},
            {text: 'Finished', value: "Finished"},
          ],
      //
      date: null,
      presetRanges: [
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
        {
          label: 'This year (slot)',
          range: [startOfYear(new Date()), endOfYear(new Date())],
          slot: 'yearly',
        },
      ],

      //
      isPriorityModalOpen: false,
      field1: null,
      field2: null,

    }
  },
  mounted() {

    const date = new Date(Date.now() - 12.5 * 60 * 60 * 1000); // Subtract 2.5 hours

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Ensure two digits
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");

    // this.moreParams.date =`${year}-${month}-${day} ${hours}:${minutes}`

    this.setFixturesManualResult()
    this.setCountries()
    this.setSports()
    this.setTournaments()
  },
  watch: {
    //
    async live_prematch_select(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.moreParams.odds_live = newVal.value
        await this.setFixturesManualResult()
      }
    }
  },
  methods: {
    ...mapActions(["getFixturesManualResult", "updateFixtureScores", "getCountries", "getSports", "getTournaments", "toggleSideMenu", "getBetSlipCount"]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    async performSearch() {
      await this.setFixturesManualResult()
    },

    async onCountryChange(value) {
      if (!value) {
        this.moreParams.country = ''
        this.tournamentsParams.country = ''
        this.moreParams.page = 1
        await this.setFixturesManualResult()
        await this.setTournaments()
      } else {
        this.moreParams.country = value.label ?? this.country.label
        this.tournamentsParams.country = value.label ?? this.country.label
        this.moreParams.page = 1
        await this.setFixturesManualResult()
        await this.setTournaments()
      }
    },

    async onSportChange(value) {
      if (!value) {
        this.moreParams.sport_id = ''
        this.tournamentsParams.sport_id = ''
        this.moreParams.page = 1
        await this.setFixturesManualResult()
        await this.setTournaments()
      } else {
        this.moreParams.sport_id = value.value ?? this.sport.value
        this.tournamentsParams.sport_id = value.value ?? this.sport.value
        this.moreParams.page = 1
        await this.setFixturesManualResult()
        await this.setTournaments()
      }
    },

    async onTournamentChange(value) {
      if (!value) {
        this.moreParams.tournament_id = ''
        this.moreParams.page = 1
        await this.setFixturesManualResult()
      } else {
        this.moreParams.tournament_id = value.value ?? this.tournament.value
        this.moreParams.page = 1
        await this.setFixturesManualResult()
      }
    },

    async selectDate() {
      let vm = this
      vm.moreParams.start = vm.formatDate(this.date[0])
      vm.moreParams.end = vm.formatDate(this.date[1])
      vm.moreParams.timestamp = Date.now()

      // vm.moreParams.start_time = formattedDate;

      await vm.setFixturesManualResult()
    },

    async handleMatchStatusChange() {
      // console.log("Match Status changed:", this.match_status_select);
      // Call your function or trigger any logic here

      this.moreParams.status = this.match_status_select.value
      await this.setFixturesManualResult()
    },

    formatDate(date) {
      var d = new Date(date),
          month = '' + (d.getMonth() + 1),
          day = '' + d.getDate(),
          year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    // Pagination and dropdowns
    gotToPage(page) {
      let vm = this
      vm.moreParams.page = page
      vm.offset = page
      vm.setFixturesManualResult()
    },

    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    toggleDetails(data) {
      this.viewModelOpen = true;
      this.deposit = data;
    },

    openPriorityModal(data, index) {
      this.isPriorityModalOpen = true;
      this.fixture = data;

      // Reset update data fields
      this.updateData = {
        ht: { home: '', away: '' },
        ft: { home: '', away: '' },
        et: { home: '', away: '' },
        corners: { home: '', away: '' },
        yellowCards: { home: '', away: '' },
        redCards: { home: '', away: '' },
        penaltiesScore: { home: '', away: '' },
      };

      // Pre-fill with existing data if available
      if (data.ht_scores) {
        const htScores = data.ht_scores.split('-');
        if (htScores.length === 2) {
          this.ht.home = htScores[0];
          this.ht.away = htScores[1];
        }
      }

      if (data.ft_scores) {
        const ftScores = data.ft_scores.split('-');
        if (ftScores.length === 2) {
          this.ft.home = ftScores[0];
          this.ft.away = ftScores[1];
        }
      }

      // Set up fixture params
      this.updateFixtureParams = {
        match_id: data.match_id,
        half_time_scores: '',
        full_time_scores: '',
        extra_time_scores: '',
        extra_results: '',
        resulting_type: 'Manual',
        timestamp: Date.now()
      };

      this.showDropdown[index] = false;
    },

    closeModal() {
      this.isPriorityModalOpen = false;
    },
    // Update
    async updateFix(data) {
      let app = this

      console.log('updateFix', JSON.stringify(data));

      if (data==='void') {
        // Handle void case
        app.updateFixtureParams.half_time_scores = "void";
        app.updateFixtureParams.full_time_scores = "void";
      } else {
        // Validate required fields
        if (app.ht.home === '' || app.ht.away === '' ||
            app.ft.home === '' || app.ft.away === ''
        ) {
          await Swal.fire({
            icon: "error",
            title: "Validation Error",
            text: "Halftime Score and Full-time Score are required!",
          });
          return; // Stop execution if validation fails
        }

        // Validate score logic
        if (parseInt(app.ht.home) > parseInt(app.ft.home) ||
            parseInt(app.ht.away) > parseInt(app.ft.away)
        ) {
          await Swal.fire({
            icon: "error",
            title: "Score Validation Error",
            text: "Halftime Score cannot be higher than Full-time Score!",
          });
          return; // Stop execution if validation fails
        }

        // Assign values if validation passes
        app.updateFixtureParams.half_time_scores = `${app.ht.home}-${app.ht.away}`;
        app.updateFixtureParams.full_time_scores = `${app.ft.home}-${app.ft.away}`;
        app.updateFixtureParams.extra_time_scores = `${app.et.home}-${app.et.away}`;

        // Build extra payload with all available data
        let extraPayload = {
          corners: `${app.corners.home || 0}-${app.corners.away || 0}`,
          yellow_cards: `${app.yellowCards.home || 0}-${app.yellowCards.away || 0}`,
          red_cards: `${app.redCards.home || 0}-${app.redCards.away || 0}`,
          penalties: `${app.penaltiesScore.home || 0}-${app.penaltiesScore.away || 0}`,
        };

        app.updateFixtureParams.extra_results = JSON.stringify(extraPayload);
      }

      console.log("updateFixtureParams: ", JSON.stringify(app.updateFixtureParams))
      // return
      // Add timestamp and match_id to ensure we're updating the correct fixture
      app.updateFixtureParams.timestamp = Date.now();
      app.updateFixtureParams.match_id = app.fixture.match_id;

      // Confirm with user before proceeding
      app.$swal.fire({
        title: 'Are you sure?',
        text: data ? "This will void the fixture results!" : "This will update the fixture scores!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (_) => {
          app.isLoading = true
          return await this.updateFixtureScores(app.updateFixtureParams)
        },
      })
      .then(async (response) => {
        app.isLoading = false

        if (response.value && response.value.status === 200) {
          // Add the updated match_id to recentlyUpdatedIds
          if (!app.recentlyUpdatedIds.includes(app.fixture.match_id)) {
            app.recentlyUpdatedIds.push(app.fixture.match_id);
          }
          
          app.$swal.fire({
            title: 'Updated!',
            text: response.value.message,
            icon: 'success'
          }).then(async (_) => {
            await app.setFixturesManualResult()
          })
        } else if (response.value) {
          app.$swal.fire('Error!', response.value.message, 'error')
        }
      })

      app.closeModal()
    },

    async setFixturesManualResult() {
      let app = this
      app.isLoading = true

      const params = new URLSearchParams();

      for (const key in app.moreParams) {
        if (app.moreParams.hasOwnProperty(key)) {
          params.append(key, app.moreParams[key]);
        }
      }
      const queryString = params.toString();
      let response = await this.getFixturesManualResult(queryString)
      if (response.status === 200) {
        // Check for updated records
        const currentTime = Date.now();
        const updatedRecords = response.message.result.filter(fixture => {
          // Parse the updated timestamp (adjust field name if needed)
          const updatedTime = new Date(fixture.updated_at || fixture.last_updated || fixture.created_at).getTime();
          // Check if it's within the highlight duration (30 minutes)
          return (currentTime - updatedTime) < this.highlightDuration;
        });
        
        // Store the match_ids of recently updated records
        this.recentlyUpdatedIds = updatedRecords.map(fixture => fixture.match_id);
        console.log("Recently updated IDs:", this.recentlyUpdatedIds);
        
        app.fixtures = response.message.result;
        if (response.message.record_count !== 0) {
          // Convert string to number to fix the type error
          app.total = parseInt(response.message.record_count, 10);
        }
        
        app.showDropdown = []
        for (let i = 0; i < app.fixtures.length; i++) {
          app.showDropdown.push(false)
        }
      } else {
        app.fixtures = [];
        app.total = 0;
      }
      app.isLoading = false
    },

    async setCountries() {
      let app = this
      app.isLoading = true

      const params = new URLSearchParams();

      for (const key in app.countryParams) {
        if (app.countryParams.hasOwnProperty(key)) {
          params.append(key, app.countryParams[key]);
        }
      }
      const queryString = params.toString();
      let response = await this.getCountries(queryString)
      if (response.status === 200) {
        response.message.result.forEach(function (item) {
          let list = {label: item.country, value: parseInt(item.id)}
          app.countries.push(list)
        })
      }
      app.isLoading = false
    },

    async setSports() {
      let app = this
      app.isLoading = true

      const params = new URLSearchParams();

      for (const key in app.sportsParams) {
        if (app.sportsParams.hasOwnProperty(key)) {
          params.append(key, app.sportsParams[key]);
        }
      }
      const queryString = params.toString();
      let response = await this.getSports(queryString)
      if (response.status === 200) {

        response.message.result.forEach(function (item) {
          let list = {label: item.sport_name, value: parseInt(item.sport_id)}
          app.sports.push(list)
        })
      }
      app.isLoading = false
    },

    async setTournaments() {
      let app = this
      app.isLoading = true
      app.tournamentsParams.limit = 10000
      const params = new URLSearchParams();
      for (const key in app.tournamentsParams) {
        if (app.tournamentsParams.hasOwnProperty(key)) {
          params.append(key, app.tournamentsParams[key]);
        }
      }
      const queryString = params.toString();
      let response = await this.getTournaments(queryString)
      if (response.status === 200) {
        app.tournaments = []
        response.message.result.forEach(function (item) {
          let list = {label: item.tournament_name, value: parseInt(item.tournament_id)}
          app.tournaments.push(list)
        })
      } else {
        app.tournaments = []
      }
      app.isLoading = false
    },

    async turnCoverageOnOff(num) {
      console.log('turnCoverageOnOff', num);
    },

    // Add method to check if a match was recently updated
    isRecentlyUpdated(matchId) {
      return this.recentlyUpdatedIds.includes(matchId);
    },
    resetFilters() {
      // Reset all filter parameters
      this.moreParams = {
        team_name: '',
        sport_id: '',
        match_id: '',
        game_id: '',
        tournament_id: '',
        live_coverage: '',
        pre_match_coverage: '',
        country: '',
        status: '',
        sort: '',
        start_time: '',
        start: '',
        end: '',
        page: '',
        limit: "100",
        timestamp: 'timestamp',
        skip_cache: '',
      };
      
      // Reset all select values
      this.sport = '';
      this.country = '';
      this.tournament = '';
      this.match_status_select = '';
      this.date = null;
      
      // Refresh the fixtures list
      this.setFixturesManualResult();
    },
  },
}
</script>

<style scoped>
.search-field {
  position: relative;
}

.search-select {
  font-size: 0.875rem;
}

/* Animation for highlighting */
@keyframes highlight-pulse {
  0% { background-color: rgba(254, 240, 138, 0.7); }
  50% { background-color: rgba(254, 240, 138, 0.3); }
  100% { background-color: rgba(254, 240, 138, 0.7); }
}

.highlight-row {
  animation: highlight-pulse 2s infinite;
}

/* Improve table appearance */
:deep(.data-table th) {
  padding: 0.75rem 1rem;
}

:deep(.data-table tr:hover) {
  background-color: rgba(249, 250, 251, 1);
}

:deep(.data-table td) {
  padding: 0.75rem 1rem;
}


/* Date picker styles */
:deep(.dp__input) {
  padding: 0.25rem 2rem;
  font-size: 0.75rem;
  line-height: 1rem;
  border-radius: 0.375rem;
}

:deep(.dp__main) {
  font-size: 0.75rem;
}
</style>
