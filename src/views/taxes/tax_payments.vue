<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />
    <page-header pageName="Tax" pageSubtitle="Payments" />

    <!-- Export Section -->
    <div class="flex justify-between items-center px-3 mt-4 mb-4">
      <h2 class="text-lg font-semibold text-gray-700">Tax Payments</h2>
      <div class="flex flex-wrap gap-2">
        <!-- Excel Export -->
        <excel-export v-if="taxPayments.length > 0" :headers="exportHeaders" :items="taxPayments"
          :file-title="'Tax Payments - ' + moment(new Date()).format('YYYY-MM-DD')" button-text="Export to Excel"
          sheet-name="Tax Payments" />

        <!-- CSV Export Buttons -->
        <div v-if="!download.request && !download.loading" class="flex flex-wrap gap-2">
          <!-- Export All -->
          <button
            class="inline-flex items-center px-3 py-2 rounded-md bg-blue-500 text-white hover:bg-blue-600 transition-colors text-sm"
            @click.prevent="downLoadTaxPayments('')">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export All
          </button>

          <!-- Export Paid -->
          <button
            class="inline-flex items-center px-3 py-2 rounded-md bg-green-500 text-white hover:bg-green-600 transition-colors text-sm"
            @click.prevent="downLoadTaxPayments(1)">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Export Paid
          </button>

          <!-- Export Unpaid -->
          <button
            class="inline-flex items-center px-3 py-2 rounded-md bg-orange-500 text-white hover:bg-orange-600 transition-colors text-sm"
            @click.prevent="downLoadTaxPayments(2)">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
            </svg>
            Export Unpaid
          </button>

          <!-- Export Failed -->
          <button
            class="inline-flex items-center px-3 py-2 rounded-md bg-red-500 text-white hover:bg-red-600 transition-colors text-sm"
            @click.prevent="downLoadTaxPayments(3)">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
            Export Failed
          </button>
        </div>

        <!-- Loading State -->
        <button v-else-if="!download.request && download.loading"
          class="inline-flex items-center px-4 py-2 rounded-md bg-blue-500 text-white">
          <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
            viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
            </path>
          </svg>
          Processing...
        </button>

        <!-- Download Component -->
        <download v-else :file-title="download.fileTitle" :headers="download.headers" :items="download.items" />
      </div>
    </div>

    <div class="block p-3">
      <auto-table :headers="tableHeaders" :data="taxPayments" :loading="isLoading" :total-items="total"
        :items-per-page="limit" :current-page-prop="offset" :server-side-pagination="true" :pagination="total > limit"
        :show-items-count="true" :decimal-places="decimalPlaces" :items-per-page-options="[10, 25, 50, 100]"
        @page-change="gotToPage" @items-per-page-change="handleLimitChange">
        <!-- ID Column -->
        <template #id="{ item }">
          <strong>{{ item.id }}</strong>
        </template>

        <!-- PRN Number Column -->
        <template #prn_number="{ item }">
          <span class="font-medium">{{ item.prn_number }}</span>
        </template>

        <!-- PRN Amount Column -->
        <template #prn_amount="{ item }">
          <span class="font-medium">{{ formatNumber(item.prn_amount) }}</span>
        </template>

        <!-- PRN Reg Date Column -->
        <template #prn_reg_date="{ item }">
          <span class="font-medium">{{ item.prn_reg_date }}</span>
        </template>

        <!-- Tax Type Column -->
        <template #tax_type="{ item }">
          <span class="font-medium">{{ item.tax_type }}</span>
        </template>

        <!-- Response Description Column -->
        <template #response_description="{ item }">
          <strong>{{ item.response_description }}</strong>
        </template>

        <!-- Receipt Number Column -->
        <template #reciept_number="{ item }">
          <strong>{{ item.reciept_number }}</strong>
        </template>

        <!-- Status Column -->
        <template #status="{ item }">

          <button v-if="parseInt(item.status) === 200"
            class="inline-block px-4 py-1 rounded-md text-white bg-green-500">
            Paid
          </button>
          <button v-else class="inline-block px-4 py-1 rounded-md text-white bg-orange-500">
            UnPaid
          </button>

        </template>

        <!-- Status Column -->
        <template #status_reason="{ item }">
          <button v-if="parseInt(item.status) === 200"
            class="inline-block px-4 py-1 rounded-md text-white bg-green-500">
            Paid
          </button>
          <button v-else-if="parseInt(item.status) === 301"
            class="inline-block px-4 py-1 rounded-md text-white bg-red-500">
            Bad Req
          </button>
          <button v-else-if="parseInt(item.status) === 599"
            class="inline-block px-4 py-1 rounded-md text-white bg-red-500">
            No Saf Auth Tk
          </button>
          <button v-else-if="parseInt(item.status) === 400"
            class="inline-block px-4 py-1 rounded-md text-white bg-orange-500">
            UnPaid
          </button>

        </template>

        <!-- Date Column -->
        <template #created_at="{ item }">
          <span style="font-size: 11px; color: grey">{{ moment(item.created_at).format('llll') }}</span>
        </template>

        <!-- Actions Column -->
        <template #actionsw="{ item, index }">
          <div class="relative" style="position: static;">
            <action-dropdown button-text="Actions" :show-text="false" menu-class="action-dropdown">
              <!-- Repost Option -->
              <action-item text="Repost Payment" color="red" @click="activateDeActivate(item, 0)">
                <template #icon>
                  <svg class="mr-3 h-5 w-5 text-red-500 group-hover:text-red-600" xmlns="http://www.w3.org/2000/svg"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </template>
              </action-item>

              <!-- View Accounts Option -->
              <action-item text="View Accounts" color="blue" @click="viewAccounts(item)">
                <template #icon>
                  <svg class="mr-3 h-5 w-5 text-blue-500 group-hover:text-blue-600" xmlns="http://www.w3.org/2000/svg"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </template>
              </action-item>

            </action-dropdown>
          </div>
        </template>

        <template #actions="{ item, index }">
          <div class="relative z-10">
            <action-dropdown v-if="(parseInt(item.status) !== 200)"
              button-text="Actions" :show-text="false" button-class="z-50" menu-class="z-50 origin-top-right"
              :menu-width="48">
              <action-item text="View Bet Slip" color="blue" @click="viewBetSlip(item)">
                <template #icon>
                  <svg class="mr-3 h-5 w-5 text-blue-500 group-hover:text-blue-600" xmlns="http://www.w3.org/2000/svg"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </template>
              </action-item>

              <action-item text="Resettle Bet" color="blue" @click="resettleBet(item)">
                <template #icon>
                  <svg class="mr-3 h-5 w-5 text-blue-500 group-hover:text-blue-600" xmlns="http://www.w3.org/2000/svg"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </template>
              </action-item>

              <action-item text="Cancel Bet" color="blue" @click="cancelBet(item)">
                <template #icon>
                  <svg class="mr-3 h-5 w-5 text-red-500 group-hover:text-blue-600" xmlns="http://www.w3.org/2000/svg"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </template>
              </action-item>
            </action-dropdown>
            
          </div>
        </template>

      </auto-table>

      <!--Modals-->
      <!--Blacklist Modal-->
      <div v-if="taxPayment != null" class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
        :class="{ 'opacity-100 pointer-events-auto': viewModelOpen, 'opacity-0 pointer-events-none': !viewModelOpen }">
        <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
        <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
          <div class="modal-content py-4 text-left px-6">
            <!--Title -->
            <div class="flex justify-between items-center pb-3">
              <p class="text-2xl font-bold">Callback Extra Data</p>
              <div class="modal-close cursor-pointer z-50" @click="viewModelOpen = false">
                <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                  viewBox="0 0 18 18">
                  <path
                    d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z" />
                </svg>
              </div>
            </div>
            <!--Body -->
            <div class="mb-4">
              <label v-if="resultCode" class="block text-sm font-medium">Result Code : {{ resultCode }}</label>
            </div>

            <div class="mb-4">
              <label v-if="debitPartyCharges" class="block text-sm font-medium">Debit Party Charges : {{
                debitPartyCharges
                }}</label>
            </div>

            <div class="mb-4">
              <label v-if="transCompletedTime" class="block text-sm font-medium">Trans Completed Time : +{{
                transCompletedTime
                }}</label>
            </div>

            <div class="mb-4">
              <label v-if="debitAccountBalance" class="block text-sm font-medium">Debit Account Balance : {{
                debitAccountBalance }}</label>
            </div>

            <div class="mb-4">
              <label v-if="receiverPartyPublicName" class="block text-sm font-medium">Receiver Party Public Name : {{
                receiverPartyPublicName }}</label>
            </div>

            <div class="mb-4">
              <label v-if="initiatorAccountCurrentBalance" class="block text-sm font-medium">Initiator Account Current
                Balance
                : {{ initiatorAccountCurrentBalance }}</label>
            </div>

            <div class="mb-4">
              <label v-if="debitPartyAffectedAccountBalance" class="block text-sm font-medium">Debit Party Affected
                Account
                Balance : {{ debitPartyAffectedAccountBalance }}</label>
            </div>

          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
import moment from "moment";
import { mapActions } from "vuex";
import CustomLoading from '@/components/common/CustomLoading.vue';
import PageHeader from '@/components/common/PageHeader.vue';
import { AutoTable } from '@/components/common';
import Download from "@/components/downloadCSV.vue";
import { formatNumber } from "accounting";

export default {
  components: {
    Download,
    CustomLoading,
    PageHeader,
    AutoTable
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 100,
      offset: 1,
      showDropdown: [],
      viewModelOpen: false,
      //
      selectedStatus: 'All', // Initially, no filter (show all)
      //
      tableHeaders: [
        // { key: 'id', label: 'Id', align: 'left' },
        { key: 'prn_number', label: 'PRN Number', align: 'left' },
        { key: 'prn_amount', label: 'PRN Amount', align: 'left' },
        { key: 'prn_reg_date', label: 'PRN Reg Date', align: 'left' },
        { key: 'tax_type', label: 'Tax Type', align: 'left' },
        { key: 'response_description', label: 'Response Description', align: 'left' },
        { key: 'reciept_number', label: 'Receipt No', align: 'left' },
        { key: 'status', label: 'Status', align: 'center' },
        { key: 'status_reason', label: 'Status Reason', align: 'center' },
        { key: 'created_at', label: 'Date', align: 'left' },
        { key: 'actions', label: 'Actions', align: 'center' },
      ],
      decimalPlaces: {
        prn_amount: 2
      },
      //
      taxPayments: [],
      taxPayment: null,
      resultCode: null,
      debitPartyCharges: null,
      transCompletedTime: null,
      debitAccountBalance: null,
      receiverPartyPublicName: null,
      initiatorAccountCurrentBalance: null,
      debitPartyAffectedAccountBalance: null,
      //
      moreParams: {
        page: '',
        timestamp: 'timestamp',
        skip_cache: '',
        start: '',
        end: '',
        limit: "100",
        export: "",
        export_paid: "",
        export_unpaid: "",
        export_failed: "",
      },

      download: { request: false, loading: false, headers: {}, items: [], fileTitle: '' },

      // Excel export headers with better formatting
      exportHeaders: {
        prn_number: 'PRN NO.',
        prn_amount: 'PRN AMOUNT.',
        prn_reg_date: 'PRN REG DATE.',
        tax_type: 'TAX TYPE.',
        response_description: 'RESPONSE DESCRIPTION.',
        reciept_number: 'RECEIPT NO.',
        created_at: 'DATE.',
      },

    }
  },
  mounted() {
    this.setTaxPayments()
  },
  methods: {
    ...mapActions(["getTaxPayments", "payTax", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },


    // Format numbers with commas
    formatNumber(value) {
      if (!value) return '0.00';
      return parseFloat(value).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
    },

    // Handle items per page change
    handleLimitChange(newLimit) {
      this.limit = newLimit;
      this.moreParams.limit = newLimit.toString();
      this.offset = 1;
      this.moreParams.page = '1';
      this.setTaxPayments();
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },
    // Filtering
    filterByStatus() {
      console.log("this.selectedStatus", this.selectedStatus)
      if (this.selectedStatus === "All") {
        this.taxPayments = this.taxPayments; // Show all if no filter
      } else {
        this.taxPayments = this.taxPayments.filter(depositData => parseInt(depositData.callback_status) === parseInt(this.selectedStatus));
      }
    },


    //
    async downLoadTaxPayments(key) {
      this.isLoading = true

      // Reset all export parameters first
      delete this.moreParams.export_paid;
      delete this.moreParams.export_unpaid;
      delete this.moreParams.export_failed;

      this.moreParams.export = "1"

      // Set specific export type based on key
      if (key === 1) {
        this.moreParams.export_paid = 1
      } else if (key === 2) {
        this.moreParams.export_unpaid = 1
      } else if (key === 3) {
        this.moreParams.export_failed = 1
      }
      // If key is empty string or other value, export all (no additional parameters)
      const params = new URLSearchParams();

      for (const key in this.moreParams) {
        if (this.moreParams.hasOwnProperty(key)) {
          params.append(key, this.moreParams[key]);
        }
      }

      const queryString = params.toString();

      // console.log("Params: " + queryString);

      let response = await this.getTaxPayments(queryString)

      if (response.status === 200) {
        this.download.items = response.message.result
        this.download.headers = {
          prn_number: 'PRN NO.',
          prn_amount: 'PRN AMOUNT.',
          prn_reg_date: 'PRN REG DATE.',
          tax_type: 'TAX TYPE.',
          response_description: 'RESPONSE DESCRIPTION.',
          reciept_number: 'RECEIPT NO.',
          created_at: 'DATE.',
        }

        // Set specific file title based on export type
        let exportType = 'All';
        if (key === 1) {
          exportType = 'Paid';
        } else if (key === 2) {
          exportType = 'Unpaid';
        } else if (key === 3) {
          exportType = 'Failed';
        }

        this.download.fileTitle = `Tax Payments (${exportType}) - ${moment(new Date()).format('lll')}`
        this.download.loading = false
        this.download.request = true
      }

      this.isLoading = false
    },


    // Pagination and dropdowns
    gotToPage(page) {
      let vm = this
      vm.moreParams.page = page
      vm.offset = page
      vm.setTaxPayments()
    },
    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },
    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    toggleDetails(data) {
      this.viewModelOpen = true;
      this.taxPayment = data;
      // Parse the JSON string in "extra_data" field
      const extraData = JSON.parse(data.callback_extra_data);
      console.log("extraData:", extraData)

      // Extract specific properties
      this.resultCode = extraData.ResultCode;
      this.debitPartyCharges = extraData.DebitPartyCharges;
      this.transCompletedTime = extraData.TransCompletedTime;
      this.debitAccountBalance = extraData.DebitAccountBalance;
      this.receiverPartyPublicName = extraData.ReceiverPartyPublicName;
      this.initiatorAccountCurrentBalance = extraData.InitiatorAccountCurrentBalance;
      this.debitPartyAffectedAccountBalance = extraData.DebitPartyAffectedAccountBalance;
    },

    async setTaxPayments() {
      this.isLoading = true

      const params = new URLSearchParams();

      for (const key in this.moreParams) {
        if (this.moreParams.hasOwnProperty(key)) {
          params.append(key, this.moreParams[key]);
        }
      }

      const queryString = params.toString();

      // console.log("Params: " + queryString);

      let response = await this.getTaxPayments(queryString)

      // console.log("taxPayments OK: " + JSON.stringify(response.message.result))
      if (response.status === 200) {
        this.taxPayments = response.message.result
        this.total = parseInt(response.message.record_count)

        this.showDropdown = []
        for (let i = 0; i < this.taxPayments.length; i++) {
          this.showDropdown.push(false)
        }
      }

      this.filterByStatus()
      this.isLoading = false
    },

    //
    async payTaxes(item) {
      let payload = {
        "timestamp": Date.now(),
        "id": item.prn_number,
      }

      // console.log("ikjsbdncm",JSON.stringify(payload))

      this.$swal.fire({
        title: 'Are you sure?',
        text: "Confirm this action please!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, confirm!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async () => {
          return await this.payTax(payload)
        },
      }).then(async result => {
        if (result.value.status === 200) {
          this.$swal.fire('Submitted!', result.value.message, 'success')
        } else {
          this.$swal.fire('Error!', result.value.message, 'error')
        }
      })
    },




    //
  },
}
</script>

<style scoped>
/* Export buttons responsive styles */
@media (max-width: 768px) {
  .flex.justify-between {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .flex.flex-wrap.gap-2 {
    width: 100%;
    justify-content: flex-start;
  }

  .flex.flex-wrap.gap-2>div {
    width: 100%;
  }

  .flex.flex-wrap.gap-2 button {
    flex: 1;
    min-width: 0;
    justify-content: center;
  }

  .text-sm {
    font-size: 0.75rem;
  }

  .px-3 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}

@media (max-width: 640px) {
  .flex.flex-wrap.gap-2>div {
    flex-direction: column;
    gap: 0.5rem;
  }

  .flex.flex-wrap.gap-2 button {
    width: 100%;
  }
}

/* Button hover effects */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Ensure buttons don't get too small */
button {
  min-height: 2.5rem;
}
</style>
