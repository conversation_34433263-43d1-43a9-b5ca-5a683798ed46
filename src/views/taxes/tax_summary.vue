<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />
    <page-header pageName="Tax" pageSubtitle="Summary" />

    <div class="block p-3 ">
      <!-- Date Filter Section -->
      <div class="mb-4">
        <div class="filter-card p-3 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-2">Date Filter</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-xs text-gray-600 mb-1">Date Range</label>
              <VueDatePicker
                v-model="date"
                range
                :multi-calendars="!isMobile"
                :enable-time-picker="false"
                :format="'yyyy-MM-dd'"
                :preset-ranges="presetRanges"
                placeholder="Select date range"
                class="w-full text-xs responsive-datepicker"
                @change="selectDate"
                @update:model-value="selectDate"
              />
            </div>
            <div class="flex items-end">
              <button @click="applyFilters()"
                class="px-4 py-2 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700 transition-colors">
                Apply Filters
              </button>
              <button @click="resetFilters()"
                class="ml-2 px-4 py-2 bg-gray-200 text-gray-700 text-sm rounded-md hover:bg-gray-300 transition-colors">
                Reset
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Export Section -->
      <div class="flex justify-between items-center px-0 mb-4">
        <h2 class="text-lg font-semibold text-gray-700">Tax Summary</h2>
        <div class="flex space-x-3">
          <excel-export v-if="taxSummary.length > 0" :headers="exportHeaders" :items="taxSummary"
            :file-title="'Tax Summary - ' + moment(new Date()).format('YYYY-MM-DD')" button-text="Export to Excel"
            sheet-name="Tax Summary" />
          <button v-if="!download.request && !download.loading"
            class="inline-flex items-center px-4 py-2 rounded-md bg-blue-500 text-white hover:bg-blue-600 transition-colors"
            @click.prevent="downLoadTaxSummary()">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export CSV
          </button>
          <button v-else-if="!download.request && download.loading"
            class="inline-flex items-center px-4 py-2 rounded-md bg-blue-500 text-white">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
              viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
              </path>
            </svg>
            Processing...
          </button>
          <download v-else :file-title="download.fileTitle" :headers="download.headers" :items="download.items" />
        </div>
      </div>

      <auto-table :data="taxSummary" :loading="isLoading" :total-items="total" :items-per-page="limit" :decimal-places="decimalPlaces"
        :current-page-prop="offset" :server-side-pagination="true" :pagination="total > limit" :show-items-count="true"
        :exclude-columns=excludeColumns :table-margin-bottom="0" :items-per-page-options="[10, 25, 50, 100]"
        @page-change="gotToPage" @items-per-page-change="handleLimitChange" >

        <!--  -->
        <template #summary_date ="{ item }">
            <span>{{ moment(item.summary_date).format('ll') }}</span>
          </template>

      </auto-table>

    </div>

  </div>
</template>

<script>
import moment from "moment";
import { mapActions } from "vuex";
import Download from "@/components/downloadCSV.vue";
import CustomLoading from '@/components/common/CustomLoading.vue';
import PageHeader from '@/components/common/PageHeader.vue';
import ExcelExport from '@/components/ExcelExport.vue';
import AutoTable from "@/components/common/AutoTable.vue";
import VueDatePicker from '@vuepic/vue-datepicker';

export default {
  components: {
    Download,
    AutoTable,
    CustomLoading,
    PageHeader,
    ExcelExport,
    VueDatePicker
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 100,
      offset: 1,
      showDropdown: [],
      viewModelOpen: false,
      //
      taxSummary: [],
      //
      selectedStatus: 'All', // Initially, no filter (show all)
      filteredTaxSummary: this.taxSummary, // Initially same as taxSummary
      //
      // Date filter
      date: null,
      windowWidth: window.innerWidth,
      presetRanges: [
        { label: 'Today', range: [new Date(), new Date()] },
        { label: 'Yesterday', range: [new Date(Date.now() - 24 * 60 * 60 * 1000), new Date(Date.now() - 24 * 60 * 60 * 1000)] },
        { label: 'This week', range: [new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), new Date()] },
        { label: 'Last 7 days', range: [new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), new Date()] },
        { label: 'Last 30 days', range: [new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), new Date()] },
        { label: 'This month', range: [new Date(new Date().getFullYear(), new Date().getMonth(), 1), new Date()] },
        { label: 'Last month', range: [new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1), new Date(new Date().getFullYear(), new Date().getMonth(), 0)] },
        { label: 'This year', range: [new Date(new Date().getFullYear(), 0, 1), new Date()] }
      ],

      moreParams: {
        page: '',
        timestamp: 'timestamp',
        skip_cache: '',
        summary_date: '',
        start: '',
        end: '',
        limit: "100",
        export: "",
      },
      excludeColumns: [
        "trx_count",
        "updated_at",
        "summary_id",
      ],
      decimalPlaces:{
        total_stke:2,
        total_payout:2,
        ggr:2,
        excirce_tax:2,
        witholding_tax:2,
        betting_tax:2,
        tax_factor:2,
      },

      download: { request: false, loading: false, headers: {}, items: [], fileTitle: '' },

      // Excel export headers with better formatting
      exportHeaders: {
        // summary_id: 'Summary ID',
        total_bets: 'Total Bets',
        total_stke: 'Total Stake',
        total_payout: 'Total Payout',
        total_deposits: "Total Deposits",
        total_withdrawals: "Total Withdrawals",
        ggr: 'Total GGR',
        excise_tax: 'Excise Tax',
        witholding_tax: 'Withholding Tax',
        betting_tax: "Betting Tax",
        tax_factor: "Tax Factor",
        summary_date: 'Summary Date',
        created_at: "Record Date"
      },
    }
  },
  computed: {
    isMobile() {
      return this.windowWidth <= 768;
    }
  },
  mounted() {
    this.setTaxSummary();
    window.addEventListener('resize', this.handleResize);
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    ...mapActions(["getTaxSummary","", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
      // this.isSideMenuOpen = !this.isSideMenuOpen;
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    handleResize() {
      this.windowWidth = window.innerWidth;
    },

    // Format date for API
    formatDate(date) {
      return moment(date).format('YYYY-MM-DD');
    },

    // Handle date selection
    async selectDate() {
      // If date is null (cleared), reset date filters and fetch data
      if (!this.date) {
        console.log('Date filter cleared, resetting and fetching data...');
        this.moreParams.start = '';
        this.moreParams.end = '';
        this.moreParams.timestamp = Date.now();
        await this.setTaxSummary();
        return;
      }

      // If date range is incomplete, return without doing anything
      if (!this.date[0] || !this.date[1]) return;

      // Update date filter values
      this.moreParams.start = this.formatDate(this.date[0]);
      this.moreParams.end = this.formatDate(this.date[1]);
      this.moreParams.timestamp = Date.now();

      // Log that date filter was updated and fetch data
      console.log('Date filter updated, fetching data with new date range...');
      await this.setTaxSummary();
    },

    // Apply filters
    async applyFilters() {
      this.offset = 1;
      this.moreParams.page = '1';
      await this.setTaxSummary();
    },

    // Reset filters
    async resetFilters() {
      this.date = null;
      this.moreParams.start = '';
      this.moreParams.end = '';
      this.moreParams.timestamp = Date.now();
      this.offset = 1;
      this.moreParams.page = '1';
      await this.setTaxSummary();
    },

    // Handle items per page change
    handleLimitChange(newLimit) {
      this.limit = newLimit;
      this.moreParams.limit = newLimit.toString();
      this.offset = 1;
      this.moreParams.page = '1';
      this.setTaxSummary();
    },
   
    // Pagination and dropdowns
    gotToPage(page) {
      let vm = this
      vm.moreParams.page = page
      vm.offset = page
      vm.setTaxSummary()
    },

    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    toggleDetails(data) {
      this.viewModelOpen = true;
      this.gameTransaction = data;
      // Parse the JSON string in "extra_data" field
      const extraData = JSON.parse(data.extra_data);
      // console.log("extraData:", extraData)

      // Access individual properties
      this.gameTransactionId = extraData.game_transaction_id;
      this.gameRoundId = extraData.game_round_id;
      this.msisdn = extraData.msisdn;
      this.trx_id = extraData.trx_id;
      this.selection = extraData.selection;
      this.total_odd = extraData.total_odd;
      this.org_balance = extraData.org_balance;
      this.withdrawal_dlr_id = extraData.withdrawal_dlr_id;
    },

    async setTaxSummary() {
      this.isLoading = true

      const params = new URLSearchParams();

      for (const key in this.moreParams) {
        if (this.moreParams.hasOwnProperty(key)) {
          params.append(key, this.moreParams[key]);
        }
      }

      const queryString = params.toString();

      // console.log("Params getTaxSummary: " + queryString);

      let response = await this.getTaxSummary(queryString)

      // console.log("taxSummary OK: " + JSON.stringify(response.message.result))
      if (response.status === 200) {

        this.taxSummary = response.message.result
        this.total = parseInt(response.message.record_count)

        this.showDropdown = []
        for (let i = 0; i < this.taxSummary.length; i++) {
          this.showDropdown.push(false)
        }
      }

      this.isLoading = false
    },

    //
    async downLoadTaxSummary() {
      this.isLoading = true

      this.moreParams.export = "1"
      const params = new URLSearchParams();

      for (const key in this.moreParams) {
        if (this.moreParams.hasOwnProperty(key)) {
          params.append(key, this.moreParams[key]);
        }
      }

      const queryString = params.toString();

      // console.log("Params: " + queryString);

      let response = await this.getTaxSummary(queryString)

      this.download.items = response.message.result
      this.download.headers = {
        summary_id: 'SUMMARY ID',
        total_bets: 'TOTAL BETS',
        total_stke: 'TOTAL STAKE',
        total_payout: 'TOTAL PAYOUT',
        total_ggr: 'TOTAL GGR',
        excise_tax: 'EXCISE TAX',
        witholding_tax: 'WITHOLDING TAX',
        summary_date: 'SUMMARY DATE',
      }
      this.download.fileTitle = 'Tax Summary as at - ' + moment(new Date()).format('lll')
      this.download.loading = false
      this.download.request = true

      this.isLoading = false
    },

    //
  },
}
</script>

<style>
@import '@vuepic/vue-datepicker/dist/main.css';
</style>

<style scoped>
/* Table styles */
table {
  border-collapse: separate;
  border-spacing: 0;
}

thead {
  background-color: #f9fafb;
}

th {
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

tbody tr:hover {
  background-color: #f9fafb;
}

/* Pagination styles */
.pagination-button {
  transition: all 0.2s ease;
}

.pagination-button:hover {
  background-color: #f3f4f6;
}

/* Modal styles */
.modal {
  transition: all 0.3s ease;
}

.modal-container {
  max-height: 90vh;
}

/* Date picker responsive styles */
.responsive-datepicker :deep(.dp__input) {
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
  line-height: 1rem;
  border-radius: 0.375rem;
  border: 1px solid #d1d5db;
  width: 100%;
  background-color: white;
  transition: all 0.2s ease-in-out;
}

.responsive-datepicker :deep(.dp__input:hover) {
  border-color: #a5b4fc;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.responsive-datepicker :deep(.dp__input:focus) {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Filter card styles */
.filter-card {
  background-color: #ffffff;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.filter-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Mobile responsive improvements */
@media (max-width: 768px) {
  .responsive-datepicker :deep(.dp__input) {
    padding: 0.75rem 1rem;
    font-size: 1rem;
    min-height: 48px;
  }

  .filter-card {
    padding: 1rem !important;
  }

  .grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }
}
</style>
