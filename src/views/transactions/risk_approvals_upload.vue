<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Risk" pageSubtitle="Approvals Upload" />
    
    <!-- Upload Button and inline Upload area -->
    <div class="px-6 py-4 border-b border-gray-200 flex flex-col space-y-4">
      
      <button 
        @click="triggerFileInput"
        class="self-start px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors flex items-center"
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
        </svg>
        Upload Approvals
      </button>

      <!-- Hidden file input -->
      <input
        ref="fileInput"
        type="file"
        class="hidden"
        accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        @change="handleFileUpload"
      />

      <!-- Show upload file info once uploaded and decoded -->
      <div v-if="uploadedFile" class="p-4 bg-gray-50 rounded-md border border-gray-300 max-w-xl">
        <div class="flex items-center justify-between">
          <div>
            <p class="font-semibold text-gray-700 truncate">{{ uploadedFile.name }}</p>
            <p class="text-xs text-gray-500">{{ formatFileSize(uploadedFile.size) }}</p>
          </div>
          <button @click="removeFile" class="text-red-500 hover:text-red-700" title="Remove file">
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div v-if="parsingError" class="mt-2 text-red-600 text-sm font-semibold">
          Failed to decode file: {{ parsingError }}
        </div>

        <div v-if="decodedData.length > 0" class="mt-2 max-h-40 overflow-auto text-sm text-gray-700">
          <strong>Parsed Data (first 5 records):</strong>
          <ul class="list-disc pl-5 mt-1">
            <li v-for="(item, idx) in decodedData.slice(0,5)" :key="idx">
              {{ item.phone_number }} - {{ item.amount }} - {{ item.source }}
            </li>
          </ul>
          <p v-if="decodedData.length > 5" class="italic text-gray-500">...and {{ decodedData.length - 5 }} more records</p>
        </div>
      </div>

      <!-- Reason and Description -->
      <div class="max-w-xl space-y-4 mt-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1" for="upload-reason">Reason</label>
          <input 
            id="upload-reason"
            type="text"
            v-model="uploadReason"
            placeholder="Enter reason for upload"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1" for="upload-description">Description</label>
          <textarea
            id="upload-description"
            v-model="uploadDescription"
            rows="3"
            placeholder="Enter description"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          ></textarea>
        </div>
      </div>

      <!-- Submit button -->
      <div class="max-w-xl flex justify-end mt-4">
        <button
          @click="submitUpload"
          :disabled="decodedData.length === 0 || isUploading"
          :class="{'opacity-50 cursor-not-allowed': decodedData.length === 0 || isUploading}"
          class="px-4 py-2 text-sm text-white bg-indigo-600 rounded-md hover:bg-indigo-700"
        >
          <span v-if="isUploading">Uploading...</span>
          <span v-else>Upload</span>
        </button>
      </div>

    </div>
  </div>
</template>

<script>
import * as XLSX from "xlsx";
import { mapActions } from "vuex";
import CustomLoading from '@/components/common/CustomLoading.vue'; // Add this import
import PageHeader from '@/components/common/PageHeader.vue';
import Swal from 'sweetalert2/dist/sweetalert2.js'; // Add this import

export default {
  components: {
    CustomLoading,
    PageHeader
  },
  data() {
    return {
      uploadedFile: null,
      decodedData: [],
      parsingError: '',
      uploadReason: '',
      uploadDescription: '',
      isUploading: false,
      isLoading: false,
    };
  },
  methods: {
    ...mapActions(["riskApprovalsUpload", "toggleSideMenu"]),

    triggerFileInput() {
      this.$refs.fileInput.click();
    },
    removeFile() {
      this.uploadedFile = null;
      this.decodedData = [];
      this.parsingError = '';
      this.uploadReason = '';
      this.uploadDescription = '';
      const input = this.$refs.fileInput;
      if (input) input.value = '';
    },
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    async handleFileUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      const validTypes = [
        'text/csv',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ];
      if (!validTypes.includes(file.type)) {
        Swal.fire({
          title: 'Error!',
          text: 'Please upload a valid CSV or Excel file',
          icon: 'error'
        });
        this.removeFile();
        return;
      }

      this.uploadedFile = file;
      this.decodedData = [];
      this.parsingError = '';

      try {
        const content = await this.readFileAsync(file);
        let rows = [];

        if(file.type === 'text/csv' || file.name.toLowerCase().endsWith('.csv')) {
          rows = this.parseCSV(content);
        } else {
          // Excel parsing
          const workbook = XLSX.read(content, { type: 'binary' });
          const sheetName = workbook.SheetNames[0];
          rows = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName], { header: 1 });
        }

        if (!rows.length) {
          throw new Error('File is empty or invalid format');
        }

        // Find column indexes for phone_number, amount and source (case insensitive)
        const headers = rows[0].map(h => h.toString().toLowerCase().trim());
        const phoneIndex = headers.indexOf('phone_number');
        const amountIndex = headers.indexOf('amount');
        const sourceIndex = headers.indexOf('source');

        if (phoneIndex === -1 || amountIndex === -1 || sourceIndex === -1) {
          throw new Error("File must contain 'phone_number', 'amount', and 'source' columns");
        }

        // Process data rows into objects
        this.decodedData = rows.slice(1).reduce((acc, row) => {
          if (row[phoneIndex]) {
            acc.push({
              phone_number: row[phoneIndex].toString(),
              amount: row[amountIndex],
              source: row[sourceIndex].toString()
            });
          }
          return acc;
        }, []);

        if (this.decodedData.length === 0) {
          throw new Error('No valid data found in the file');
        }

      } catch (error) {
        this.parsingError = error.message || 'Error parsing file';
        this.decodedData = [];
        Swal.fire({
          title: 'Error!',
          text: "Error parsing file: " + this.parsingError,
          icon: 'error'
        });
      }
    },
    parseCSV(text) {
      // Simple CSV parsing for demo (assumes commas only, no quoted commas)
      return text.split(/\r?\n/).map(line => line.split(','));
    },
    readFileAsync(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = reject;
        if (file.type === 'text/csv' || file.name.toLowerCase().endsWith('.csv')) {
          reader.readAsText(file);
        } else {
          reader.readAsBinaryString(file);
        }
      });
    },
    async submitUpload() {
      if (this.decodedData.length === 0) {
        Swal.fire({
          title: 'Error!',
          text: 'Please upload and decode a valid file first',
          icon: 'error'
        });
        return;
      }

      this.isUploading = true;

      try {
        // Build payload matching backend expectations
        const payload = {
          timestamp: new Date().toISOString(),
          reason: this.uploadReason,
          description: this.uploadDescription,
          status: '0',
          user_data: this.decodedData
        };

        // Assuming riskApprovalsUpload accepts plain object now
        const result = await this.riskApprovalsUpload(payload);

        if (result.status === 200) {
          Swal.fire({
            title: 'Success!',
            text: `Risk approvals uploaded successfully`,
            icon: 'success'
          });
          // this.$toast.success('Risk approvals uploaded successfully', { position: 'top-right', duration: 3000 });
          this.removeFile();
        } else {
          throw new Error(result.message || 'Upload failed');
        }
      } catch (error) {
        Swal.fire({
          title: 'Error!',
          text: error.message || 'Upload failed',
          icon: 'error'
        });
      } finally {
        this.isUploading = false;
      }
    },

  }
}
</script>
